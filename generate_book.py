#!/usr/bin/env python3
"""
<PERSON> for Book Generation

This script chains together all steps of the book generation process:
1. Summary Generation
2. Term Extraction
3. Puzzle Generation
4. Solution Generation
5. Document Generation

Run this script from the project root directory to generate the complete book.
"""

import sys
import time
import subprocess
import shutil
from pathlib import Path

def run_step(step_name, script_path, extra_args=None):
    """Run a step in the book generation process."""
    print(f"\n{'=' * 80}")
    print(f"STEP: {step_name}")
    print(f"{'=' * 80}")
    print(f"Running: {script_path}")
    sys.stdout.flush()  # Force output to be displayed immediately

    start_time = time.time()

    # Prepare command
    cmd = [sys.executable, script_path]
    if extra_args:
        cmd.extend(extra_args)
        print(f"With extra arguments: {extra_args}")

    # Run the script with output displayed in real-time
    result = subprocess.run(cmd,
                           stdout=None,  # Display output in real-time
                           stderr=None,  # Display errors in real-time
                           text=True)

    # Check if the script ran successfully
    if result.returncode != 0:
        print(f"Error running {step_name}. Return code: {result.returncode}")
        sys.exit(1)

    end_time = time.time()
    duration = end_time - start_time

    print(f"\nCompleted {step_name} in {duration:.2f} seconds")
    sys.stdout.flush()  # Force output to be displayed immediately

    return result

def clear_directory(directory, keep_dir=True, preserve_files=None):
    """Clear all files in a directory, optionally preserving specific files.

    Args:
        directory: The directory to clear
        keep_dir: Whether to keep the directory structure (True) or remove it entirely (False)
        preserve_files: List of filenames to preserve (e.g., ['topics.txt'])
    """
    if preserve_files is None:
        preserve_files = []

    dir_path = Path(directory)

    if dir_path.exists():
        if keep_dir:
            # Remove all files in the directory except those in preserve_files
            file_count = 0
            for item in dir_path.iterdir():
                if item.is_file() and item.name not in preserve_files:
                    item.unlink()
                    file_count += 1
                elif item.is_file() and item.name in preserve_files:
                    print(f"    Preserving {item.name} in {dir_path}")
                elif item.is_dir():
                    shutil.rmtree(item)
                    file_count += 1
            if file_count > 0:
                print(f"    Removed {file_count} items from {dir_path}")
            else:
                print(f"    Directory {dir_path} was already empty or only contained preserved files")
        else:
            # If we're removing the entire directory, first save the files to preserve
            preserved_contents = {}
            for filename in preserve_files:
                file_path = dir_path / filename
                if file_path.exists():
                    with open(file_path, 'rb') as f:
                        preserved_contents[filename] = f.read()
                    print(f"    Temporarily saving {filename} before removing directory")

            # Remove the entire directory
            print(f"    Removing directory {dir_path}")
            shutil.rmtree(dir_path)
    else:
        print(f"    Directory {dir_path} does not exist yet")

    # Recreate the directory if it was removed or doesn't exist
    if not dir_path.exists() or not keep_dir:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"    Created directory {dir_path}")

        # Restore preserved files if we removed the entire directory
        if not keep_dir:
            for filename, content in preserved_contents.items():
                file_path = dir_path / filename
                with open(file_path, 'wb') as f:
                    f.write(content)
                print(f"    Restored {filename} to {dir_path}")

    sys.stdout.flush()  # Force output to be displayed immediately

def copy_files(source_dir, dest_dir, pattern="*"):
    """Copy files from source directory to destination directory."""
    source_path = Path(source_dir)
    dest_path = Path(dest_dir)

    print(f"    Copying {pattern} from {source_path} to {dest_path}")
    sys.stdout.flush()  # Force output to be displayed immediately

    # Ensure destination directory exists
    dest_path.mkdir(parents=True, exist_ok=True)

    # Find files matching the pattern
    files = list(source_path.glob(pattern))

    if not files:
        print(f"    No files matching {pattern} found in {source_path}")
        sys.stdout.flush()
        return 0

    # Copy each file
    for file in files:
        dest_file = dest_path / file.name
        with open(file, 'rb') as src, open(dest_file, 'wb') as dst:
            dst.write(src.read())
        print(f"    Copied {file.name}")

    print(f"    Copied {len(files)} files total")
    sys.stdout.flush()  # Force output to be displayed immediately

    return len(files)

def cleanup_intermediate_files(project_root):
    """Clean up intermediate files, keeping only summaries and the final document."""
    print("\nCleaning up intermediate files...")
    sys.stdout.flush()

    # We want to keep:
    # 1. Summaries in 1-Summary Generation/output
    # 2. Final document in 5-Document Generation/output
    # We'll clean up everything else

    # Clean up Term Extraction files
    print("  Cleaning up term extraction files...")
    clear_directory(project_root / "2-Extract Terms" / "input")
    clear_directory(project_root / "2-Extract Terms" / "output")

    # Clean up Puzzle Generation files
    print("  Cleaning up puzzle generation files...")
    clear_directory(project_root / "3-Puzzle Generation" / "input")
    clear_directory(project_root / "3-Puzzle Generation" / "output")

    # Clean up Solution files
    print("  Cleaning up solution files...")
    clear_directory(project_root / "4-Puzzle Solutions" / "input")
    clear_directory(project_root / "4-Puzzle Solutions" / "output")

    # Clean up Document Generation input files (but keep the output)
    print("  Cleaning up document generation input files...")
    clear_directory(project_root / "5-Document Generation" / "input")

    print("  Cleanup complete!")
    sys.stdout.flush()

    return None

def main(clear_summaries=False, skip_summaries=False, term_method=None, maintain_order=False):
    # Get the project root directory
    project_root = Path(__file__).resolve().parent

    # Define the steps and their scripts
    steps = [
        {
            "name": "Summary Generation",
            "script": project_root / "1-Summary Generation" / "scripts" / "generate_summaries.py",
            "pre_step": None,
            "clear_output": clear_summaries,  # Only clear if explicitly requested
            "skip": skip_summaries  # Skip this step if summaries already exist
        },
        {
            "name": "Term Extraction",
            "script": project_root / "2-Extract Terms" / "scripts" / "extract_terms.py",
            "pre_step": lambda: copy_files(
                project_root / "1-Summary Generation" / "output",
                project_root / "2-Extract Terms" / "input",
                "*.md"
            ),
            "clear_output": True,
            "skip": False
        },
        {
            "name": "Puzzle Generation",
            "script": project_root / "3-Puzzle Generation" / "scripts" / "generate_word_search.py",
            "pre_step": lambda: copy_files(
                project_root / "2-Extract Terms" / "output",
                project_root / "3-Puzzle Generation" / "input",
                "*_numbers.json"
            ),
            "clear_output": True,
            "skip": False
        },
        {
            "name": "Solution Generation",
            "script": project_root / "4-Puzzle Solutions" / "scripts" / "generate_solutions.py",
            "pre_step": None,  # The solution script reads directly from the puzzle generation output
            "clear_output": True,
            "skip": False
        },
        {
            "name": "Document Generation",
            "script": project_root / "5-Document Generation" / "scripts" / "generate_docx.py",
            "pre_step": None,  # The document generation script reads from all previous outputs
            "clear_output": True,
            "skip": False
        }
    ]

    # Run each step
    total_start_time = time.time()

    print("Starting book generation process...")
    print(f"Project root: {project_root}")

    # Clear all directories at the beginning
    print("\nClearing directories...")
    for step in steps:
        step_name = step["name"]
        # Use the correct directory structure with numbers and spaces
        step_num = steps.index(step) + 1
        # Convert "Term Extraction" to "Extract Terms" etc.
        if step_name == "Term Extraction":
            dir_name = "Extract Terms"
        elif step_name == "Puzzle Generation":
            dir_name = "Puzzle Generation"
        elif step_name == "Solution Generation":
            dir_name = "Puzzle Solutions"
        elif step_name == "Document Generation":
            dir_name = "Document Generation"
        else:
            dir_name = step_name

        step_dir = project_root / f"{step_num}-{dir_name}"

        # Clear input directory, preserving topics.txt for the Summary Generation step
        input_dir = step_dir / "input"
        preserve_files = ["topics.txt"] if step_name == "Summary Generation" else []
        clear_directory(input_dir, preserve_files=preserve_files)
        print(f"  Cleared {input_dir}" + (" (preserved topics.txt)" if preserve_files else ""))

        # Clear output directory if specified
        if step["clear_output"]:
            output_dir = step_dir / "output"
            clear_directory(output_dir)
            print(f"  Cleared {output_dir}")
        else:
            print(f"  Keeping {step_name} output directory intact")

    # Run each step
    for i, step in enumerate(steps):
        step_name = step["name"]
        step_num = i + 1

        # Convert "Term Extraction" to "Extract Terms" etc.
        if step_name == "Term Extraction":
            dir_name = "Extract Terms"
        elif step_name == "Puzzle Generation":
            dir_name = "Puzzle Generation"
        elif step_name == "Solution Generation":
            dir_name = "Puzzle Solutions"
        elif step_name == "Document Generation":
            dir_name = "Document Generation"
        else:
            dir_name = step_name

        # Skip this step if requested
        if step["skip"]:
            print(f"\nSkipping {step_name}...")
            continue

        # Run pre-step if defined
        if step["pre_step"]:
            print(f"\nPreparing for {step_name}...")
            num_files = step["pre_step"]()
            print(f"Copied {num_files} files")

        # Run the main step
        extra_args = []

        # Add term method argument if this is the term extraction step and a method is specified
        if step_name == "Term Extraction" and term_method:
            extra_args.extend(["--method", term_method])

        # Add maintain-order argument if this is the document generation step and maintain_order is True
        if step_name == "Document Generation" and maintain_order:
            extra_args.append("--maintain-order")

        run_step(step_name, step["script"], extra_args if extra_args else None)

    # Clean up intermediate files
    cleanup_intermediate_files(project_root)

    total_end_time = time.time()
    total_duration = total_end_time - total_start_time

    print("\n" + "=" * 80)
    print(f"BOOK GENERATION COMPLETE in {total_duration:.2f} seconds")
    print("=" * 80)

    # Print the location of the final document
    final_doc = project_root / "5-Document Generation" / "output" / "word_search_book.docx"
    print(f"\nFinal document generated at: {final_doc}")
    print("Intermediate files have been cleaned up.")
    print("\nNext steps:")
    print("1. Open the DOCX file in Pages")
    print("2. Enable 'Facing Pages' setting to get inside/outside margins")
    print("3. Set the margins: Top: 0.75\", Bottom: 0.75\", Inside: 0.63\", Outside: 0.38\"")
    print("4. Add page numbers to the bottom outside corners")
    print("5. Save as .pages format")
    print("6. Export as PDF/A if needed")

if __name__ == "__main__":
    print("Starting generate_book.py script...")
    sys.stdout.flush()
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate a word search book")
    parser.add_argument(
        "--clear-summaries",
        action="store_true",
        help="Clear summary output directory (WARNING: This will require regenerating summaries using the OpenAI API)"
    )
    parser.add_argument(
        "--skip-summaries",
        action="store_true",
        help="Skip summary generation if summaries already exist (saves API costs)"
    )
    parser.add_argument(
        "--term-method",
        choices=["default", "chatgpt_musician", "chatgpt_year", "chatgpt_genre", "chatgpt_topic"],
        help="Override the term extraction method for all topics"
    )
    parser.add_argument(
        "--maintain-order",
        action="store_true",
        help="Maintain the order of topics from topics.txt in the final document"
    )
    args = parser.parse_args()

    # Check if summaries exist
    summary_dir = Path("1-Summary Generation/output")
    summary_files = list(summary_dir.glob("*.md"))

    # If summaries exist and --skip-summaries is not specified, ask the user if they want to skip
    if summary_files and not args.skip_summaries and not args.clear_summaries:
        print(f"Found {len(summary_files)} existing summaries.")
        print("To save API costs, you can skip summary generation.")
        print("Use --skip-summaries to skip summary generation.")
        print("Use --clear-summaries to regenerate summaries.")
        print("Continuing in 5 seconds...")
        sys.stdout.flush()
        time.sleep(5)

    # Automatically skip summaries if they exist and --clear-summaries is not specified
    skip_summaries = args.skip_summaries or (summary_files and not args.clear_summaries)

    # Run the main function with the provided arguments
    main(clear_summaries=args.clear_summaries, skip_summaries=skip_summaries,
         term_method=args.term_method, maintain_order=args.maintain_order)
