#!/usr/bin/env python3
"""
Math Problem Generation Script

This script generates math problem pages for a Number Search Book.
Each page contains 20 math problems (addition, subtraction, multiplication, or division).
"""

import sys
import random
from pathlib import Path

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir

def read_topics(file_path):
    """Read topics from a text file in format: 'Puzzle Name|operation_type'."""
    topics = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and '|' in line:
                name, operation = line.split('|', 1)
                topics.append((name.strip(), operation.strip()))
            elif line:  # Fallback for old format
                topics.append((line, 'mixed'))
    return topics

def generate_math_problem(operation, target_digits):
    """Generate a single math problem with answer in target digit range and operands 2-5 digits."""
    if target_digits == 'short':  # 5 digits
        min_answer, max_answer = 10000, 99999
    elif target_digits == 'medium':  # 6 digits
        min_answer, max_answer = 100000, 999999
    elif target_digits == 'long':  # 7 digits
        min_answer, max_answer = 1000000, 9999999
    else:
        min_answer, max_answer = 10000, 99999  # Default to short

    # Operand constraints: 2-5 digits (10 to 99999)
    min_operand, max_operand = 10, 99999

    # Simplified approach: generate operands first, then calculate answer
    if operation == 'addition':
        # Generate two operands within range
        a = random.randint(min_operand, max_operand)
        b = random.randint(min_operand, max_operand)
        answer = a + b

        # If answer is not in target range, adjust
        if answer < min_answer:
            # Make operands larger
            a = random.randint(min_answer // 2, max_operand)
            b = random.randint(min_answer - a, max_operand)
            answer = a + b
        elif answer > max_answer:
            # Make operands smaller
            a = random.randint(min_operand, max_answer // 2)
            b = random.randint(min_operand, max_answer - a)
            answer = a + b

        return (a, b), answer

    elif operation == 'subtraction':
        # Generate operands ensuring positive result
        a = random.randint(min_answer + min_operand, max_operand)  # a must be big enough
        b = random.randint(min_operand, min(max_operand, a - min_answer))  # b can't make result too small
        answer = a - b

        # Ensure answer is in target range
        if answer > max_answer:
            b = random.randint(a - max_answer, min(max_operand, a - min_answer))
            answer = a - b

        return (a, b), answer

    elif operation == 'multiplication':
        # Generate factors within operand range
        a = random.randint(min_operand, min(max_operand, 999))  # Keep reasonable
        b = random.randint(min_operand, min(max_operand, 999))  # Keep reasonable
        answer = a * b

        # Adjust if answer is not in target range
        if answer < min_answer:
            # Need larger operands
            a = random.randint(max(min_operand, int(min_answer ** 0.5)), max_operand)
            b = random.randint(max(min_operand, min_answer // a), min(max_operand, max_answer // a))
            answer = a * b
        elif answer > max_answer:
            # Need smaller operands
            a = random.randint(min_operand, min(max_operand, int(max_answer ** 0.5)))
            b = random.randint(min_operand, min(max_operand, max_answer // a))
            answer = a * b

        return (a, b), answer

    else:
        # Fallback to addition
        return generate_math_problem('addition', target_digits)

def generate_math_problems(operation_type, count=20):
    """Generate a list of math problems based on operation type."""
    problems = []
    answers = []
    operations_used = []

    if operation_type == 'mixed':
        # Distribute across 3 operation types (no division)
        operations = ['addition'] * 7 + ['subtraction'] * 7 + ['multiplication'] * 6
        random.shuffle(operations)
    else:
        operations = [operation_type] * count

    # Distribute problems across different answer lengths (5, 6, 7 digits)
    target_distributions = ['short'] * 7 + ['medium'] * 7 + ['long'] * 6  # 7 five-digit, 7 six-digit, 6 seven-digit
    random.shuffle(target_distributions)

    for i in range(count):
        operation = operations[i]
        target_digits = target_distributions[i]
        operands, answer = generate_math_problem(operation, target_digits)
        problems.append(operands)
        answers.append(answer)
        operations_used.append(operation)

    return problems, answers, operations_used

def save_math_problems(puzzle_name, operation_type, problems, answers, operations_used, output_dir, order=1):
    """Save the generated math problems to a markdown file with YAML frontmatter."""
    # Create a filename-friendly version of the puzzle name
    filename = puzzle_name.lower().replace(' ', '_') + '.md'
    output_path = output_dir / filename

    # Convert tuples to lists for YAML compatibility
    problems_as_lists = [[operand1, operand2] for operand1, operand2 in problems]

    # Create YAML frontmatter using proper YAML formatting
    import yaml
    frontmatter_data = {
        'title': puzzle_name,
        'order': order,
        'operation_type': operation_type,
        'problem_count': len(problems),
        'answers': answers,
        'operations': operations_used,
        'problems': problems_as_lists
    }

    yaml_frontmatter = "---\n" + yaml.dump(frontmatter_data, default_flow_style=False) + "---\n\n"

    # Create the content
    content = f"# {puzzle_name}\n\n"
    content += "Solve each problem below, then find the answers in the number search puzzle on the facing page.\n\n"

    # Get the operation symbol
    op_symbols = {
        'addition': '+',
        'subtraction': '-',
        'multiplication': '×',
        'division': '÷'
    }

    # Format problems in 2 columns, 10 rows with clear operation display
    left_column_problems = problems[:10]
    left_column_ops = operations_used[:10]
    right_column_problems = problems[10:20] if len(problems) > 10 else []
    right_column_ops = operations_used[10:20] if len(operations_used) > 10 else []

    for i in range(10):
        # Left column
        if i < len(left_column_problems):
            left_operands = left_column_problems[i]
            left_op = left_column_ops[i]
            left_symbol = op_symbols.get(left_op, '+')
            left_text = f"{left_operands[0]:,} {left_symbol} {left_operands[1]:,} = _______"
        else:
            left_text = ""

        # Right column
        if i < len(right_column_problems):
            right_operands = right_column_problems[i]
            right_op = right_column_ops[i]
            right_symbol = op_symbols.get(right_op, '+')
            right_text = f"{right_operands[0]:,} {right_symbol} {right_operands[1]:,} = _______"
        else:
            right_text = ""

        # Add both problems side by side with spacing
        content += f"{left_text:<50} {right_text}\n\n"

    with open(output_path, 'w') as f:
        f.write(yaml_frontmatter)
        f.write(content)

    print(f"Math problems for '{puzzle_name}' saved to {output_path} with {len(problems)} problems")

def main():
    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = base_dir / 'output'

    # Ensure output directory exists
    ensure_dir(output_dir)

    # Read topics
    topics_file = input_dir / 'topics.txt'
    if not topics_file.exists():
        print(f"Topics file not found at {topics_file}")
        return

    topics = read_topics(topics_file)
    topic_count = len(topics)

    if topic_count == 0:
        print("No topics found in the topics.txt file.")
        return
    else:
        print(f"Found {topic_count} topics to process")

    # Generate and save math problems for each topic
    for i, (puzzle_name, operation_type) in enumerate(topics, 1):
        print(f"[{i}/{topic_count}] Generating math problems for: {puzzle_name} ({operation_type})")
        
        # Validate operation type
        valid_operations = ['addition', 'subtraction', 'multiplication', 'division', 'mixed']
        if operation_type not in valid_operations:
            print(f"Warning: Unknown operation type '{operation_type}'. Using 'mixed' instead.")
            operation_type = 'mixed'
        
        problems, answers, operations_used = generate_math_problems(operation_type)
        save_math_problems(puzzle_name, operation_type, problems, answers, operations_used, output_dir, order=i)

    print("\nMath problem generation complete!")
    print(f"Generated {topic_count} puzzle pages in {output_dir}")
if __name__ == "__main__":
    main()
