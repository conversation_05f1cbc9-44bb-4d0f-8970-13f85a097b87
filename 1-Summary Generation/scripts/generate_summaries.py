#!/usr/bin/env python3
"""
Summary Generation Script

This script generates engaging summaries of educational topics for a general audience using the OpenAI API.
Each summary is precisely 130-150 words in length.
"""

import os
import sys
import openai
import re
from dotenv import load_dotenv
from pathlib import Path

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir

# Load environment variables from .env file
load_dotenv(Path(__file__).resolve().parents[1] / '.env')

# Configure OpenAI API
openai.api_key = os.getenv('OPENAI_API_KEY')
if not openai.api_key:
    raise ValueError("OpenAI API key not found. Please check your .env file.")

def read_topics(file_path):
    """Read topics from a text file."""
    with open(file_path, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def count_words(text):
    """Count the number of words in a text."""
    # Remove markdown formatting and count words
    text_no_md = re.sub(r'#+ |\*\*|__|~~|```[\s\S]*?```|\[.*?\]\(.*?\)', '', text)
    return len(re.findall(r'\b\w+\b', text_no_md))

def generate_summary(topic):
    """Generate an engaging summary of a topic using OpenAI API."""
    # Create an engaging and interesting biographical summary about {topic} for a general audience. 
    # Create an engaging and interesting biographical summary about the musician/band {topic} for a general audience. Talk about their rise to fame and some of their most famous or influential songs, allowing the reader to reminisce.
    prompt = f"""
    You are an expert in general knowledge, geography trivia, and a skilled writer. Craft an engaging summary about {topic} for a general audience. The summary must be exactly 130–150 words, not counting the words in the bulleted list, and based on factual information without fictional elements. Start with a compelling hook or notable fact to draw readers in, highlighting {topic}'s most fascinating aspects in clear, jargon-free language. Ensure the summary is accessible and captivating, focusing on what makes {topic} unique or intriguing. After the summary, provide a bulleted list of the exact populations of the 16 largest or most well-known cities in {topic}, each with a population exceeding 999, using the most recent, precise figures without rounding. The list must be formatted as bullet points, with each city’s name followed by its population. Ensure the summary flows naturally without headings or em dashes, and verify the summary’s word count to fall exactly within 130–150 words.
    """

    try:
        response = openai.chat.completions.create(
            model="gpt-4o-mini",  #gpt-4, gpt-4o or gpt-4o-mini
            messages=[
                {"role": "system", "content": "You are a skilled writer creating engaging, accessible summaries for a general audience. You excel at making complex topics interesting and relevant to everyday life. You are extremely precise with word counts."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.7
        )
        summary = response.choices[0].message.content

        # Verify word count
        word_count = count_words(summary)
        print(f"Generated summary with {word_count} words")

        # If word count is outside the range, try again with more specific instructions
        if word_count < 130 or word_count > 150:
            print(f"Word count {word_count} is outside the required range (130-150). Regenerating...")

            # More specific prompt for second attempt
            adjusted_prompt = f"""
            The previous summary for {topic} had {word_count} words, which does not meet the required range of 130–150 words. Please write a new summary about Alabama that is exactly between 130 and 150 words, based solely on factual information with no fictional elements. The summary must engage a general audience with clear, jargon-free language and begin with a compelling fact or hook to highlight Alabama’s unique appeal. Emphasize fascinating aspects of Alabama, such as its cultural, historical, or natural significance, and draw meaningful connections to how Alabama has influenced people’s lives. Ensure the summary flows naturally without headings or em dashes, maintaining an accessible and captivating tone. After the summary, provide a bulleted list of the exact populations of the 16 largest or most well-known cities in Alabama with populations over 999, using the most recent, unrounded figures, formatted as bullet points with each city’s name and population. The summary’s word count must be verified to fall exactly within 130–150 words, not counting the words in the bulleted list.
            """

            response = openai.chat.completions.create(
                model="gpt-4o-mini", #gpt-4, gpt-4o or gpt-4o-mini
                messages=[
                    {"role": "system", "content": "You are a skilled writer who is extremely precise with word counts. You must create a summary between 130-150 words exactly."},
                    {"role": "user", "content": adjusted_prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            summary = response.choices[0].message.content
            word_count = count_words(summary)
            print(f"Regenerated summary with {word_count} words")

        return summary
    except Exception as e:
        print(f"Error generating summary for {topic}: {e}")
        return None

def save_summary(topic, summary, output_dir, order=1):
    """Save the generated summary to a markdown file with YAML frontmatter."""
    # Create a filename-friendly version of the topic
    filename = topic.lower().replace(' ', '_') + '.md'
    output_path = output_dir / filename

    # Calculate word count
    word_count = count_words(summary)

    # Determine category (could be more sophisticated in the future)
    category = "Technology"  # Default category

    # Create YAML frontmatter
    yaml_frontmatter = f"""---
title: "{topic}"
order: {order}
category: "{category}"
word_count: {word_count}
---

"""

    with open(output_path, 'w') as f:
        f.write(yaml_frontmatter)
        f.write(f"# {topic}\n\n")
        f.write(summary)

    print(f"Summary for '{topic}' saved to {output_path} with {word_count} words")

def main():
    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = base_dir / 'output'

    # Ensure output directory exists
    ensure_dir(output_dir)

    # Read topics
    topics_file = input_dir / 'topics.txt'
    if not topics_file.exists():
        print(f"Topics file not found at {topics_file}")
        return

    topics = read_topics(topics_file)
    topic_count = len(topics)

    if topic_count == 0:
        print("No topics found in the topics.txt file.")
        return
    elif topic_count > 100:
        print(f"Warning: Found {topic_count} topics. This may take a while and incur significant API costs.")
        proceed = input("Do you want to proceed? (y/n): ")
        if proceed.lower() != 'y':
            print("Operation cancelled.")
            return
    else:
        print(f"Found {topic_count} topics to process")

    # Generate and save summaries for each topic
    for i, topic in enumerate(topics, 1):
        print(f"[{i}/{topic_count}] Generating summary for: {topic}")
        summary = generate_summary(topic)
        if summary:
            save_summary(topic, summary, output_dir, order=i)

    print("\nSummary generation complete!")
    print(f"Generated {topic_count} summaries in {output_dir}")

if __name__ == "__main__":
    main()
