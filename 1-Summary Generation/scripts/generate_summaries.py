#!/usr/bin/env python3
"""
Math Problem Generation Script

This script generates math problem pages for a Number Search Book.
Each page contains 20 math problems (addition, subtraction, multiplication, or division).
"""

import sys
import random
from pathlib import Path

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir

def read_topics(file_path):
    """Read topics from a text file in format: 'Puzzle Name|operation_type'."""
    topics = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and '|' in line:
                name, operation = line.split('|', 1)
                topics.append((name.strip(), operation.strip()))
            elif line:  # Fallback for old format
                topics.append((line, 'mixed'))
    return topics

def generate_math_problem(operation, target_digits):
    """Generate a single math problem with answer in target digit range."""
    if target_digits == 'short':  # 5 digits
        min_answer, max_answer = 10000, 99999
    elif target_digits == 'medium':  # 6 digits
        min_answer, max_answer = 100000, 999999
    elif target_digits == 'long':  # 7 digits
        min_answer, max_answer = 1000000, 9999999
    else:
        min_answer, max_answer = 10000, 99999  # Default to short

    if operation == 'addition':
        # Generate two numbers that add up to target range
        answer = random.randint(min_answer, max_answer)
        a = random.randint(1000, answer // 2)
        b = answer - a
        return (a, b), answer

    elif operation == 'subtraction':
        # Generate subtraction that results in positive answer in target range
        answer = random.randint(min_answer, max_answer)
        b = random.randint(1000, answer // 2)
        a = answer + b
        return (a, b), answer

    elif operation == 'multiplication':
        # Generate factors that multiply to target range
        answer = random.randint(min_answer, max_answer)
        # Find factors of answer or close to it
        a = random.randint(10, int(answer ** 0.5) + 100)
        b = answer // a
        actual_answer = a * b
        return (a, b), actual_answer

    elif operation == 'division':
        # Generate division that results in whole number in target range
        answer = random.randint(min_answer, max_answer)
        b = random.randint(2, 999)
        a = answer * b
        return (a, b), answer

    else:
        # Fallback to addition
        return generate_math_problem('addition', target_digits)

def generate_math_problems(operation_type, count=20):
    """Generate a list of math problems based on operation type."""
    problems = []
    answers = []
    operations_used = []

    if operation_type == 'mixed':
        # 5 of each operation type
        operations = ['addition'] * 5 + ['subtraction'] * 5 + ['multiplication'] * 5 + ['division'] * 5
        random.shuffle(operations)
    else:
        operations = [operation_type] * count

    # Distribute problems across different answer lengths (5, 6, 7 digits)
    target_distributions = ['short'] * 7 + ['medium'] * 7 + ['long'] * 6  # 7 five-digit, 7 six-digit, 6 seven-digit
    random.shuffle(target_distributions)

    for i in range(count):
        operation = operations[i]
        target_digits = target_distributions[i]
        operands, answer = generate_math_problem(operation, target_digits)
        problems.append(operands)
        answers.append(answer)
        operations_used.append(operation)

    return problems, answers, operations_used

def save_math_problems(puzzle_name, operation_type, problems, answers, operations_used, output_dir, order=1):
    """Save the generated math problems to a markdown file with YAML frontmatter."""
    # Create a filename-friendly version of the puzzle name
    filename = puzzle_name.lower().replace(' ', '_') + '.md'
    output_path = output_dir / filename

    # Create YAML frontmatter with answers and operations for extraction
    yaml_frontmatter = f"""---
title: "{puzzle_name}"
order: {order}
operation_type: "{operation_type}"
problem_count: {len(problems)}
answers: {answers}
operations: {operations_used}
problems: {problems}
---

"""

    # Create the content
    content = f"# {puzzle_name}\n\n"
    content += "Solve each problem below, then find the answers in the number search puzzle on the facing page.\n\n"

    # Get the operation symbol
    op_symbols = {
        'addition': '+',
        'subtraction': '-',
        'multiplication': '×',
        'division': '÷'
    }

    # Format problems in 2 columns, 10 rows with clear operation display
    left_column_problems = problems[:10]
    left_column_ops = operations_used[:10]
    right_column_problems = problems[10:20] if len(problems) > 10 else []
    right_column_ops = operations_used[10:20] if len(operations_used) > 10 else []

    for i in range(10):
        # Left column
        if i < len(left_column_problems):
            left_operands = left_column_problems[i]
            left_op = left_column_ops[i]
            left_symbol = op_symbols.get(left_op, '+')
            left_text = f"{left_operands[0]:,} {left_symbol} {left_operands[1]:,} = _______"
        else:
            left_text = ""

        # Right column
        if i < len(right_column_problems):
            right_operands = right_column_problems[i]
            right_op = right_column_ops[i]
            right_symbol = op_symbols.get(right_op, '+')
            right_text = f"{right_operands[0]:,} {right_symbol} {right_operands[1]:,} = _______"
        else:
            right_text = ""

        # Add both problems side by side with spacing
        content += f"{left_text:<50} {right_text}\n\n"

    with open(output_path, 'w') as f:
        f.write(yaml_frontmatter)
        f.write(content)

    print(f"Math problems for '{puzzle_name}' saved to {output_path} with {len(problems)} problems")

def main():
    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = base_dir / 'output'

    # Ensure output directory exists
    ensure_dir(output_dir)

    # Read topics
    topics_file = input_dir / 'topics.txt'
    if not topics_file.exists():
        print(f"Topics file not found at {topics_file}")
        return

    topics = read_topics(topics_file)
    topic_count = len(topics)

    if topic_count == 0:
        print("No topics found in the topics.txt file.")
        return
    else:
        print(f"Found {topic_count} topics to process")

    # Generate and save math problems for each topic
    for i, (puzzle_name, operation_type) in enumerate(topics, 1):
        print(f"[{i}/{topic_count}] Generating math problems for: {puzzle_name} ({operation_type})")
        
        # Validate operation type
        valid_operations = ['addition', 'subtraction', 'multiplication', 'division', 'mixed']
        if operation_type not in valid_operations:
            print(f"Warning: Unknown operation type '{operation_type}'. Using 'mixed' instead.")
            operation_type = 'mixed'
        
        problems, answers, operations_used = generate_math_problems(operation_type)
        save_math_problems(puzzle_name, operation_type, problems, answers, operations_used, output_dir, order=i)

    print("\nMath problem generation complete!")
    print(f"Generated {topic_count} puzzle pages in {output_dir}")

if __name__ == "__main__":
    main()
