#!/usr/bin/env python3
"""
Summary Generation Script

This script generates engaging summaries of educational topics for a general audience using the OpenAI API.
Each summary is precisely 220-240 words in length.
"""

import os
import sys
import openai
import re
from dotenv import load_dotenv
from pathlib import Path

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir

# Load environment variables from .env file
load_dotenv(Path(__file__).resolve().parents[1] / '.env')

# Configure OpenAI API
openai.api_key = os.getenv('OPENAI_API_KEY')
if not openai.api_key:
    raise ValueError("OpenAI API key not found. Please check your .env file.")

def read_topics(file_path):
    """Read topics from a text file."""
    with open(file_path, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def count_words(text):
    """Count the number of words in a text."""
    # Remove markdown formatting and count words
    text_no_md = re.sub(r'#+ |\*\*|__|~~|```[\s\S]*?```|\[.*?\]\(.*?\)', '', text)
    return len(re.findall(r'\b\w+\b', text_no_md))

def generate_summary(topic):
    """Generate an engaging summary of a topic using OpenAI API."""
    # Create an engaging and interesting biographical summary about {topic} for a general audience. 
    # Create an engaging and interesting biographical summary about the musician/band {topic} for a general audience. Talk about their rise to fame and some of their most famous or influential songs, allowing the reader to reminisce.
    prompt = f"""
    You are an expert on general knowledge, geography trivia and a talented writer. Create an engaging and interesting summary about {topic} for a general audience. The summary should discuss the population of {topic} and the 15 most highly populated, or most popular or well known, cities in {topic}. The summary should be based on facts and not include any fictional elements.
    The summary must be PRECISELY between 220 and 240 words - this is a strict requirement.

    The summary should:
    1. Be accessible and engaging for a general audience 
    2. Highlight the most fascinating aspects of {topic}.
    3. Use clear, jargon-free language
    4. Include a compelling hook or interesting and notable facts to capture interest - what makes {topic} interesting or intriguing?

    Do not use headings, bullet points, or em dashes. Write in flowing paragraphs that are easy to read.
    The word count must be between 220-240 words exactly.
    """

    try:
        response = openai.chat.completions.create(
            model="gpt-4o-mini",  #gpt-4, gpt-4o or gpt-4o-mini
            messages=[
                {"role": "system", "content": "You are a skilled writer creating engaging, accessible summaries for a general audience. You excel at making complex topics interesting and relevant to everyday life. You are extremely precise with word counts."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.7
        )
        summary = response.choices[0].message.content

        # Verify word count
        word_count = count_words(summary)
        print(f"Generated summary with {word_count} words")

        # If word count is outside the range, try again with more specific instructions
        if word_count < 220 or word_count > 240:
            print(f"Word count {word_count} is outside the required range (220-240). Regenerating...")

            # More specific prompt for second attempt
            adjusted_prompt = f"""
            The previous summary for {topic} had {word_count} words, which is outside our required range of 220-240 words.

            Please create a new summary about {topic} that is EXACTLY between 220 and 240 words.

            The summary should:
            1. Be engaging and interesting for a general audience
            2. Highlight fascinating aspects of {topic}
            3. Draw connections to how {topic} has impacted our lives
            4. Use clear, jargon-free language

            Count your words carefully before submitting. The word count must be between 220-240 words exactly.
            """

            response = openai.chat.completions.create(
                model="gpt-4o-mini", #gpt-4, gpt-4o or gpt-4o-mini
                messages=[
                    {"role": "system", "content": "You are a skilled writer who is extremely precise with word counts. You must create a summary between 220-240 words exactly."},
                    {"role": "user", "content": adjusted_prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            summary = response.choices[0].message.content
            word_count = count_words(summary)
            print(f"Regenerated summary with {word_count} words")

        return summary
    except Exception as e:
        print(f"Error generating summary for {topic}: {e}")
        return None

def save_summary(topic, summary, output_dir, order=1):
    """Save the generated summary to a markdown file with YAML frontmatter."""
    # Create a filename-friendly version of the topic
    filename = topic.lower().replace(' ', '_') + '.md'
    output_path = output_dir / filename

    # Calculate word count
    word_count = count_words(summary)

    # Determine category (could be more sophisticated in the future)
    category = "Technology"  # Default category

    # Create YAML frontmatter
    yaml_frontmatter = f"""---
title: "{topic}"
order: {order}
category: "{category}"
word_count: {word_count}
---

"""

    with open(output_path, 'w') as f:
        f.write(yaml_frontmatter)
        f.write(f"# {topic}\n\n")
        f.write(summary)

    print(f"Summary for '{topic}' saved to {output_path} with {word_count} words")

def main():
    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = base_dir / 'output'

    # Ensure output directory exists
    ensure_dir(output_dir)

    # Read topics
    topics_file = input_dir / 'topics.txt'
    if not topics_file.exists():
        print(f"Topics file not found at {topics_file}")
        return

    topics = read_topics(topics_file)
    topic_count = len(topics)

    if topic_count == 0:
        print("No topics found in the topics.txt file.")
        return
    elif topic_count > 100:
        print(f"Warning: Found {topic_count} topics. This may take a while and incur significant API costs.")
        proceed = input("Do you want to proceed? (y/n): ")
        if proceed.lower() != 'y':
            print("Operation cancelled.")
            return
    else:
        print(f"Found {topic_count} topics to process")

    # Generate and save summaries for each topic
    for i, topic in enumerate(topics, 1):
        print(f"[{i}/{topic_count}] Generating summary for: {topic}")
        summary = generate_summary(topic)
        if summary:
            save_summary(topic, summary, output_dir, order=i)

    print("\nSummary generation complete!")
    print(f"Generated {topic_count} summaries in {output_dir}")

if __name__ == "__main__":
    main()
