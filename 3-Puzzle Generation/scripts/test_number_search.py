#!/usr/bin/env python3
"""
Test script for the number search puzzle generator
"""

import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).resolve().parents[2]))

from generate_word_search import (
    create_empty_grid, 
    try_place_number, 
    fill_grid, 
    generate_number_search,
    format_grid_as_text
)

def test_number_search():
    """Test the number search generation with sample numbers."""
    
    # Sample numbers of different lengths
    test_numbers = [
        "1776",      # 4 digits (short)
        "12345",     # 5 digits (short)
        "234567",    # 6 digits (short)
        "1234567",   # 7 digits (short)
        "12345678",  # 8 digits (short)
        "123456789", # 9 digits (medium)
        "1234567890", # 10 digits (medium)
        "12345678901", # 11 digits (medium)
        "123456789012", # 12 digits (medium)
        "1234567890123", # 13 digits (long)
        "12345678901234", # 14 digits (long)
        "2403",      # 4 digits (short)
        "45678",     # 5 digits (short)
        "67890",     # 5 digits (short)
        "9876",      # 4 digits (short)
        "5432",      # 4 digits (short)
        "8765",      # 4 digits (short)
        "3210"       # 4 digits (short)
    ]
    
    print("Testing Number Search Generation")
    print("=" * 40)
    print(f"Test numbers: {len(test_numbers)}")
    for i, num in enumerate(test_numbers):
        print(f"  {i+1:2d}. {num} ({len(num)} digits)")
    
    print("\nGenerating number search puzzle...")
    
    # Generate the puzzle
    grid, placed_numbers, orientation_counts = generate_number_search(test_numbers)
    
    print(f"\nResults:")
    print(f"  Placed {len(placed_numbers)} out of {len(test_numbers)} numbers")
    print(f"  Orientation distribution: {orientation_counts}")
    
    print(f"\nGenerated Grid:")
    print(format_grid_as_text(grid))
    
    print(f"\nPlaced Numbers:")
    for i, number_info in enumerate(placed_numbers):
        number = number_info["number"]
        start = number_info["start"]
        end = number_info["end"]
        orientation = number_info["orientation"]
        print(f"  {i+1:2d}. {number} - from {start} to {end} (direction: {orientation})")

if __name__ == "__main__":
    test_number_search()
