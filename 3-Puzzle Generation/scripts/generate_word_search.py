#!/usr/bin/env python3
"""
Number Search Puzzle Generator

This script generates number search puzzles using the numbers extracted in Step 2.
"""

import sys
import json
import random
import re
import argparse
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir, read_json_file, write_json_file

# Constants
GRID_SIZE = 16
ORIENTATIONS = [
    (0, 1),   # Horizontal (left to right)
    (0, -1),  # Horizontal (right to left)
    (1, 0),   # Vertical (down)
    (-1, 0),  # Vertical (up)
    (1, 1),   # Diagonal (down, right)
    (1, -1),  # Diagonal (down, left)
    (-1, 1),  # Diagonal (up, right)
    (-1, -1)  # Diagonal (up, left)
]

# Group orientations by type
HORIZONTAL_ORIENTATIONS = [(0, 1), (0, -1)]
VERTICAL_ORIENTATIONS = [(1, 0), (-1, 0)]
DIAGONAL_ORIENTATIONS = [(1, 1), (1, -1), (-1, 1), (-1, -1)]

def load_numbers(file_path):
    """Load numbers from a JSON file."""
    data = read_json_file(file_path)
    numbers = data.get("numbers", [])

    # Extract the numbers (already without spaces/commas)
    number_strings = [num.get("number", "") for num in numbers]

    # Sort by length (longest first)
    number_strings.sort(key=len, reverse=True)

    return number_strings, data.get("topic", "")

def create_empty_grid(size=GRID_SIZE):
    """Create an empty grid of the specified size."""
    return [[None for _ in range(size)] for _ in range(size)]

def is_valid_position(grid, number, row, col, row_dir, col_dir):
    """Check if a number can be placed at the specified position and orientation."""
    grid_size = len(grid)

    # Check if the number fits within the grid boundaries
    if (row + (len(number) - 1) * row_dir < 0 or
        row + (len(number) - 1) * row_dir >= grid_size or
        col + (len(number) - 1) * col_dir < 0 or
        col + (len(number) - 1) * col_dir >= grid_size):
        return False

    # Check if the number can be placed without conflicts
    for i in range(len(number)):
        r = row + i * row_dir
        c = col + i * col_dir

        # If the cell is already filled, it must match the current digit
        if grid[r][c] is not None and grid[r][c] != number[i]:
            return False

    return True

def place_number(grid, number, row, col, row_dir, col_dir):
    """Place a number in the grid at the specified position and orientation."""
    for i in range(len(number)):
        r = row + i * row_dir
        c = col + i * col_dir
        grid[r][c] = number[i]

    return {
        "number": number,
        "start": (row, col),
        "end": (row + (len(number) - 1) * row_dir, col + (len(number) - 1) * col_dir),
        "orientation": (row_dir, col_dir)
    }

def try_place_number(grid, number, orientation_types=None):
    """Try to place a number in the grid with the specified orientation types."""
    grid_size = len(grid)

    # Determine which orientations to use
    if orientation_types is None:
        orientations = ORIENTATIONS.copy()
    else:
        orientations = []
        if "horizontal" in orientation_types:
            orientations.extend(HORIZONTAL_ORIENTATIONS)
        if "vertical" in orientation_types:
            orientations.extend(VERTICAL_ORIENTATIONS)
        if "diagonal" in orientation_types:
            orientations.extend(DIAGONAL_ORIENTATIONS)

    # Shuffle orientations to randomize placement
    random.shuffle(orientations)

    # Try each orientation
    for row_dir, col_dir in orientations:
        # Create a list of all possible starting positions
        positions = [(r, c) for r in range(grid_size) for c in range(grid_size)]

        # Shuffle positions to randomize placement
        random.shuffle(positions)

        # Try each position
        for row, col in positions:
            if is_valid_position(grid, number, row, col, row_dir, col_dir):
                return place_number(grid, number, row, col, row_dir, col_dir)

    # If we get here, we couldn't place the number
    return None

def fill_grid(grid):
    """Fill the empty cells in the grid with random digits."""
    grid_size = len(grid)

    for row in range(grid_size):
        for col in range(grid_size):
            if grid[row][col] is None:
                grid[row][col] = random.choice('0123456789')

    return grid

def is_good_distribution(orientation_counts, total_numbers):
    """Check if the orientation distribution is good."""
    # We want at least 20% of numbers in each orientation type
    min_count = max(3, total_numbers * 0.2)  # At least 3 numbers or 20% of total

    # Check if any orientation type has too few numbers
    for orientation_type, count in orientation_counts.items():
        if count < min_count:
            return False

    # Check if any orientation type is too dominant (more than 50%)
    for orientation_type, count in orientation_counts.items():
        if count > total_numbers * 0.5:
            return False

    return True

def generate_number_search(numbers, max_attempts=3):
    """Generate a number search puzzle using the provided numbers.

    Args:
        numbers: List of numbers to place in the puzzle
        max_attempts: Maximum number of attempts to generate a puzzle with a good distribution

    Returns:
        Tuple of (grid, placed_numbers, orientation_counts)
    """
    best_grid = None
    best_placed_numbers = []
    best_orientation_counts = {"horizontal": 0, "vertical": 0, "diagonal": 0}
    best_score = 0

    for attempt in range(max_attempts):
        # Create an empty grid
        grid = create_empty_grid()

        # Keep track of placed numbers and their details
        placed_numbers = []

        # Keep track of how many numbers we've placed in each orientation type
        orientation_counts = {
            "horizontal": 0,
            "vertical": 0,
            "diagonal": 0
        }

        # Try to place each number
        for number in numbers:
            # Skip empty numbers or numbers that are too long
            if not number or len(number) > GRID_SIZE:
                continue

            # Determine which orientation types to prioritize
            needed_orientations = []
            for orientation_type, count in orientation_counts.items():
                if count < 3:  # We need at least 3 in each orientation
                    needed_orientations.append(orientation_type)

            # If we have enough of each orientation, use any orientation
            if not needed_orientations:
                number_info = try_place_number(grid, number)
            else:
                # Try to place the number in a needed orientation
                number_info = try_place_number(grid, number, needed_orientations)

                # If that fails, try any orientation
                if number_info is None:
                    number_info = try_place_number(grid, number)

            # If we successfully placed the number, update our tracking
            if number_info:
                placed_numbers.append(number_info)

                # Update orientation counts
                row_dir, col_dir = number_info["orientation"]
                if (row_dir, col_dir) in HORIZONTAL_ORIENTATIONS:
                    orientation_counts["horizontal"] += 1
                elif (row_dir, col_dir) in VERTICAL_ORIENTATIONS:
                    orientation_counts["vertical"] += 1
                elif (row_dir, col_dir) in DIAGONAL_ORIENTATIONS:
                    orientation_counts["diagonal"] += 1

        # Fill the remaining cells with random digits
        filled_grid = fill_grid(grid.copy())

        # Calculate a score for this attempt based on distribution and number of placed numbers
        total_numbers = len(placed_numbers)
        min_orientation = min(orientation_counts.values())

        # Distribution score is based on the minimum orientation count
        # A good distribution has at least 3-4 numbers in each orientation
        distribution_score = min_orientation / total_numbers if total_numbers > 0 else 0

        # Placement score is based on how many numbers were placed
        placement_score = total_numbers / len(numbers)

        # Heavily penalize solutions with fewer than 15 numbers
        if total_numbers < 15:
            placement_score = 0  # Reject solutions with fewer than 15 numbers

        # Weight distribution much more heavily than placement
        score = distribution_score * 0.9 + placement_score * 0.1

        print(f"  Attempt {attempt+1}/{max_attempts}: Placed {total_numbers}/{len(numbers)} numbers, distribution: {orientation_counts}, score: {score:.2f}")

        # Check if this is the best attempt so far
        if score > best_score:
            best_score = score
            best_grid = filled_grid
            best_placed_numbers = placed_numbers.copy()
            best_orientation_counts = orientation_counts.copy()

        # If we have a good distribution and placed enough numbers (all or at least 15), we can stop
        if is_good_distribution(orientation_counts, total_numbers) and (total_numbers == len(numbers) or total_numbers >= 15):
            print(f"  Found good distribution on attempt {attempt+1} with {total_numbers}/{len(numbers)} numbers, stopping early")
            break

    # Return the best result
    if best_grid is None:
        # If we didn't find any good solutions, return the last one
        return filled_grid, placed_numbers, orientation_counts
    else:
        return best_grid, best_placed_numbers, best_orientation_counts

def format_grid_as_text(grid):
    """Format the grid as a text string with space-separated digits."""
    return "\n".join(" ".join(digit) for digit in grid)

def save_number_search(grid, placed_numbers, orientation_counts, topic_name, output_dir):
    """Save the number search puzzle to JSON and text files."""
    # Convert grid to a list of strings for easier display
    grid_text = ["".join(row) for row in grid]

    # Create the output data
    output_data = {
        "topic": topic_name,
        "grid_size": len(grid),
        "grid": grid_text,
        "numbers": [number_info["number"] for number_info in placed_numbers],
        "number_locations": placed_numbers,
        "orientation_counts": orientation_counts,
        "generation_date": datetime.now().isoformat()
    }

    # Save to JSON file
    output_file = output_dir / f"{topic_name}_number_search.json"
    write_json_file(output_file, output_data)

    # Print some information about the generated puzzle
    print(f"  Placed {len(placed_numbers)} numbers")
    print(f"  Orientation counts: {orientation_counts}")
    print(f"  Number search saved to {output_file}")

    # Also save a text version for easy viewing
    text_file = output_dir / f"{topic_name}_number_search.txt"
    with open(text_file, 'w') as f:
        # Write the grid
        f.write(format_grid_as_text(grid))
        f.write("\n\n")

        # Write the number list
        f.write("Numbers to find:\n")
        # Sort by length for easier reading
        numbers = [number_info["number"] for number_info in placed_numbers]
        numbers.sort(key=len)
        for number in numbers:
            f.write(f"{number}\n")

    print(f"  Text version saved to {text_file}")

    return output_data

def main():
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Generate number search puzzles")
    parser.add_argument("--topic", help="Topic name for the puzzle")
    parser.add_argument("--numbers-file", help="Path to the numbers file")
    parser.add_argument("--output-dir", help="Directory to save the output files")
    args = parser.parse_args()

    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = Path(args.output_dir) if args.output_dir else base_dir / 'output'
    numbers_dir = Path(__file__).resolve().parents[2] / '2-Extract Terms' / 'output'

    # Ensure directories exist
    ensure_dir(input_dir)
    ensure_dir(output_dir)

    # Process a single numbers file if specified
    if args.topic and args.numbers_file:
        topic_name = args.topic
        numbers_file = Path(args.numbers_file)

        if not numbers_file.exists():
            print(f"Error: Numbers file not found: {numbers_file}")
            return 1

        print(f"Generating number search for: {topic_name}")

        # Load numbers
        numbers, topic = load_numbers(numbers_file)

        # Generate number search
        grid, placed_numbers, orientation_counts = generate_number_search(numbers)

        # Save the number search
        save_number_search(grid, placed_numbers, orientation_counts, topic_name, output_dir)

        return 0

    # Otherwise, process all numbers files
    numbers_files = list(numbers_dir.glob('*_numbers.json'))
    if not numbers_files:
        print(f"No numbers files found in {numbers_dir}")
        return 1

    print(f"Found {len(numbers_files)} numbers files to process")

    # Process each numbers file
    for numbers_file in numbers_files:
        # Get the topic name from the numbers file name
        topic_name = numbers_file.stem.replace('_numbers', '')

        # Remove timestamp from the topic name (e.g., "elvis_presley 8.30.34 AM" -> "elvis_presley")
        timestamp_match = re.search(r'\s+\d+\.\d+\.\d+\s+[AP]M', topic_name)
        if timestamp_match:
            topic_name = topic_name[:timestamp_match.start()]
        print(f"Generating number search for: {topic_name}")

        # Load numbers
        numbers, topic = load_numbers(numbers_file)

        # Generate number search
        grid, placed_numbers, orientation_counts = generate_number_search(numbers)

        # Save the number search
        save_number_search(grid, placed_numbers, orientation_counts, topic_name, output_dir)

    print("\nNumber search generation complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
