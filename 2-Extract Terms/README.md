# Step 2: Extract Numbers

This step extracts numbers (4+ digits long) from the summaries generated in Step 1. The numbers are categorized by length (short, medium, long) and will be used to generate number search puzzles in Step 3.

## Features

- **Number Extraction**: Extracts numbers that are 4 or more digits long from text
- **Comma Removal**: Automatically removes commas from numbers (e.g., "2,403" becomes "2403")
- **Length Categorization**: Automatically categorizes numbers by digit count for puzzle generation
  - Short: 4-8 digits
  - Medium: 9-12 digits  
  - Long: 13+ digits
- **Flexible Input**: Processes markdown files with YAML frontmatter from Step 1
- **JSON Output**: Saves extracted numbers in structured JSON format for Step 3

## Directory Structure

```
2-Extract Terms/
├── README.md
├── input/                    # Input directory for summary files (auto-populated from Step 1)
├── output/                   # Output directory for extracted numbers JSON files
└── scripts/                  # Python scripts for number extraction
```

- **input/**: Contains markdown summary files from Step 1 (automatically copied if empty)
- **output/**: Contains JSON files with extracted numbers for each topic

- **scripts/**: Contains the Python scripts for extracting numbers
  - `extract_terms.py`: Main wrapper script that calls extract_numbers.py
  - `extract_numbers.py`: Core script for extracting numbers from summaries
  - `test_extract_numbers.py`: Test script to demonstrate number extraction

## Process

1. Run the number extraction script: `python scripts/extract_terms.py`
   - The script will automatically copy summary files from Step 1 if needed
   - This calls the extract_numbers.py script internally
2. Review the extracted numbers in the `output/` directory

## Example Output

The script extracts numbers like:
- "2,403" → "2403" (4 digits, short)
- "1,234,567" → "1234567" (7 digits, short)
- "12,345,678,901" → "12345678901" (11 digits, medium)
- "1,000,000,000,000" → "1000000000000" (13 digits, long)

Numbers shorter than 4 digits (like "123") are ignored.

## Output Format

Each topic generates a JSON file with the following structure:

```json
{
  "topic": "Topic Name",
  "source_file": "topic_summary.md",
  "extraction_date": "2024-06-23T10:30:00",
  "total_numbers": 15,
  "short_numbers": 10,
  "medium_numbers": 3,
  "long_numbers": 2,
  "numbers": [
    {
      "number": "2403",
      "original_format": "2,403",
      "length": 4,
      "frequency": 2
    }
  ],
  "categorized": {
    "short": [...],
    "medium": [...],
    "long": [...]
  }
}
```

This format is compatible with Step 3 (Puzzle Generation) for creating number search puzzles.
