#!/usr/bin/env python3
"""
Term Extraction Script

This script extracts key terms and concepts using different methods based on the topic:
1. Default: Extract terms from summaries using OpenAI API
2. Musicians: Extract top songs using ChatGPT
3. Years: Extract top songs from that year using ChatGPT
4. Genres: Extract top songs from that genre using ChatGPT
"""

import sys
import json
import re
import yaml
import openai
import difflib  # For string similarity comparison
from pathlib import Path
from datetime import datetime
import random

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir, read_markdown_file, write_json_file, copy_files
from common.config import OPENAI_API_KEY, SUMMARY_DIR

def load_config():
    """Load configuration from config.yaml"""
    config_path = Path(__file__).resolve().parents[2] / "config.yaml"
    if config_path.exists():
        with open(config_path, "r") as f:
            return yaml.safe_load(f)
    return {"term_extraction": {"default_method": "default", "topic_methods": {}}}

# Configure OpenAI API
openai.api_key = OPENAI_API_KEY
if not openai.api_key:
    raise ValueError("OpenAI API key not found. Please check your .env file.")

def get_summary_files(input_dir):
    """Get all markdown summary files from the input directory."""
    return list(Path(input_dir).glob('*.md'))

def extract_content_from_markdown(file_path):
    """Extract content and metadata from a markdown file with YAML frontmatter."""
    content = read_markdown_file(file_path)

    # Extract YAML frontmatter if present
    yaml_pattern = re.compile(r'^---\s*\n(.*?)\n---\s*\n', re.DOTALL)
    match = yaml_pattern.match(content)

    if match:
        try:
            # Extract and parse the YAML frontmatter
            frontmatter = yaml.safe_load(match.group(1))
            # Get the content after the frontmatter
            markdown_content = content[match.end():]
            return frontmatter, markdown_content
        except yaml.YAMLError as e:
            print(f"Error parsing YAML frontmatter: {e}")

    # If no frontmatter or error parsing, return empty dict and full content
    return {}, content

def extract_terms_from_summary(summary_text, topic, max_retries=3):
    """Extract key terms from a summary using OpenAI API based on specific guidelines."""

    # Prepare the summary for term verification
    # Convert to uppercase for case-insensitive matching
    summary_upper = summary_text.upper()

    for char in '.,;:!?"()[]{}\'-_':
        summary_upper = summary_upper.replace(char, ' ')
    # Split into words for partial matching
    summary_words = set(summary_upper.split())

    # We no longer use topic-specific methods from config.yaml
    # Always use the default method for extract_terms_from_summary
    method = "default"

    # If this is a special topic type, use the appropriate extraction method
    if method == "chatgpt_musician":
        return extract_terms_for_musician(topic)
    elif method == "chatgpt_year":
        return extract_terms_for_year(topic)
    elif method == "chatgpt_genre":
        return extract_terms_for_genre(topic)
    elif method == "chatgpt_topic":
        return extract_terms_for_topic(topic)

    # No auto-detection - always use the default spaCy method unless explicitly specified

    # Otherwise, use the default extraction method

    for attempt in range(max_retries):
        if attempt > 0:
            print(f"Retry attempt {attempt}/{max_retries-1} for {topic}...")

        prompt = f"""
        I need at least 35-40 meaningful terms from the following summary about {topic}. I will filter these down to the best 18 terms, with this approximate distribution:
        - No more than 4 LONG terms (13-16 characters)
        - At least 5 MEDIUM terms (9-12 characters)
        - At least 5 SHORT terms (4-8 characters)

        Focus on extracting the most meaningful and relevant terms for the topic.

        For any topic, excellent terms would include:
        - Key people: Names of important individuals relevant to the topic
        - Specific roles or occupations: Jobs, positions, or functions of key people
        - Iconic elements: Objects, animals, vehicles, weapons, costumes, or catchphrases strongly associated with the topic
        - Concrete nouns: Tangible things rather than abstract concepts

        Follow these guidelines in priority order:
        1. Names of key people, places, or entities central to the topic
        2. Specific roles, occupations, or functions related to the topic
        3. Iconic elements or concepts strongly associated with the topic
        4. Concrete nouns and tangible concepts over abstract ideas

        Additional rules:
        - Terms must NOT contain numbers or special characters
        - Replace accented characters with non-accented versions (Å → A, Ç → C)
        - Avoid similar terms (e.g., don't include both CHINA and CHINESE)
        - Simplify terms by removing articles (e.g., use "LONE RANGER" instead of "THE LONE RANGER")
        - Avoid unnecessary two-word phrases when a single word would suffice (e.g., use "HUMOR" instead of "RELATABLE HUMOR")
        - For non-name terms, prefer single words over two-word phrases with adjectives
        - If last names make terms too similar, use first names when appropriate
        - All terms will be converted to ALL CAPS in the output
        - Character count is calculated WITHOUT spaces

        For each term, provide:
        1. The term in ALL CAPS
        2. A clear, concise definition (1-2 sentences)
        3. The length category ("long", "medium", or "short")
        4. Character count (excluding spaces)

        Format the output as a JSON array of objects with the following structure:
        [
            {{
                "term": "TERM NAME",
                "definition": "Concise definition",
                "length_category": "long",
                "char_count": 14
            }},
            ...
        ]

        IMPORTANT: Provide at least 35-40 terms so I have enough to choose from after filtering. Be thorough and creative in finding all possible terms from the summary. It's critical that you provide enough terms, as many will be filtered out during processing. If you can't find 35-40 terms in the summary, create additional relevant terms that would make sense for the topic.

        Summary:
        {summary_text}
        """

        try:
            response = openai.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a creative and precise term extractor for educational puzzles. You are extremely thorough in identifying ALL possible terms from a summary. You prioritize names of key people, specific roles, iconic elements (like animals, vehicles, weapons, costumes, catchphrases), and concrete nouns over abstract concepts. You are especially good at identifying iconic elements strongly associated with the topic, such as a hero's horse, sidekick, vehicle, or catchphrase. You simplify terms by removing articles and unnecessary words. You avoid unnecessary two-word phrases when a single word would suffice, especially for non-name terms. For example, use 'HUMOR' instead of 'RELATABLE HUMOR' and 'CLASSIC' instead of 'TIMELESS CLASSIC'. You provide at least 35-40 terms in valid, well-structured JSON format to ensure enough options after filtering. You're meticulous about character counts and categorization. It's critical to provide as many terms as possible, as many will be filtered out during processing."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.4
            )

            # Parse the JSON response
            content = response.choices[0].message.content
            # Find the JSON part in the response (in case there's additional text)
            json_start = content.find('[')
            json_end = content.rfind(']') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                terms = json.loads(json_str)

                # Process terms to add non-spaced versions and validate
                processed_terms = process_terms(terms, summary_upper, summary_words)

                # Check if we have enough terms with a reasonable distribution
                if len(processed_terms) >= 18:  # Maintain the requirement for 18 terms
                    # Group terms by category
                    long_terms_list = [term for term in processed_terms if term.get('length_category') == 'long']
                    medium_terms_list = [term for term in processed_terms if term.get('length_category') == 'medium']
                    short_terms_list = [term for term in processed_terms if term.get('length_category') == 'short']

                    # Check if we have enough terms in each category with the new distribution
                    # No minimum requirement for long terms, just maximums and minimums for medium and short
                    if len(long_terms_list) <= 4 and len(medium_terms_list) >= 5 and len(short_terms_list) >= 5:
                        # Select the best mix of terms
                        selected_terms = select_best_terms(long_terms_list, medium_terms_list, short_terms_list)
                        print(f"Successfully selected 18 terms with distribution: {sum(1 for term in selected_terms if term.get('length_category') == 'long')} long, {sum(1 for term in selected_terms if term.get('length_category') == 'medium')} medium, {sum(1 for term in selected_terms if term.get('length_category') == 'short')} short")
                        return selected_terms
                    else:
                        print(f"Warning: Got {len(processed_terms)} terms but distribution is inadequate: {len(long_terms_list)} long, {len(medium_terms_list)} medium, {len(short_terms_list)} short")

                        # Try to adjust the distribution before resorting to a full retry
                        if len(processed_terms) >= 18:
                            print("Attempting to adjust term distribution without a full retry...")
                            adjusted_terms = adjust_term_distribution(processed_terms)
                            if adjusted_terms:
                                print(f"Successfully adjusted distribution: {sum(1 for term in adjusted_terms if term.get('length_category') == 'long')} long, {sum(1 for term in adjusted_terms if term.get('length_category') == 'medium')} medium, {sum(1 for term in adjusted_terms if term.get('length_category') == 'short')} short")
                                return adjusted_terms
                            else:
                                print("Failed to adjust distribution, continuing to next retry attempt")
                        # Continue to next retry attempt
                else:
                    print(f"Warning: Only got {len(processed_terms)} terms, need at least 18")
                    # Continue to next retry attempt
            else:
                print(f"Error: Could not find valid JSON in the response for {topic}")
        except Exception as e:
            print(f"Error extracting terms for {topic}: {e}")

    # If we've exhausted all retries, try to ensure we have 18 terms
    print(f"Warning: Could not get optimal terms with correct distribution after {max_retries} attempts")

    if 'processed_terms' in locals() and processed_terms:
        # Group terms by category
        long_terms_list = [term for term in processed_terms if term.get('length_category') == 'long']
        medium_terms_list = [term for term in processed_terms if term.get('length_category') == 'medium']
        short_terms_list = [term for term in processed_terms if term.get('length_category') == 'short']

        # Use our enhanced select_best_terms function which will add generic terms if needed
        selected_terms = select_best_terms(long_terms_list, medium_terms_list, short_terms_list)
        return selected_terms
    else:
        # If we have no processed terms at all, create a set of generic terms
        print("Warning: No valid terms found. Creating generic terms for the topic.")
        generic_terms = [
            {"term": "HISTORY", "definition": "The past events related to the topic.", "length_category": "short", "char_count": 7, "term_no_spaces": "HISTORY"},
            {"term": "LEGACY", "definition": "The lasting impact or influence of the topic.", "length_category": "short", "char_count": 6, "term_no_spaces": "LEGACY"},
            {"term": "INFLUENCE", "definition": "The effect or impact of the topic on others.", "length_category": "medium", "char_count": 9, "term_no_spaces": "INFLUENCE"},
            {"term": "POPULARITY", "definition": "The state of being liked or supported by many people.", "length_category": "medium", "char_count": 10, "term_no_spaces": "POPULARITY"},
            {"term": "SIGNIFICANCE", "definition": "The importance or relevance of the topic.", "length_category": "medium", "char_count": 12, "term_no_spaces": "SIGNIFICANCE"},
            {"term": "DEVELOPMENT", "definition": "The process of growth or progress related to the topic.", "length_category": "medium", "char_count": 11, "term_no_spaces": "DEVELOPMENT"},
            {"term": "INNOVATION", "definition": "New ideas or methods introduced by or related to the topic.", "length_category": "medium", "char_count": 10, "term_no_spaces": "INNOVATION"},
            {"term": "ACHIEVEMENT", "definition": "Something accomplished or attained by the topic.", "length_category": "medium", "char_count": 11, "term_no_spaces": "ACHIEVEMENT"},
            {"term": "RECOGNITION", "definition": "Acknowledgment or appreciation for the topic.", "length_category": "medium", "char_count": 11, "term_no_spaces": "RECOGNITION"},
            {"term": "CONTRIBUTION", "definition": "Something given or provided by the topic.", "length_category": "long", "char_count": 13, "term_no_spaces": "CONTRIBUTION"},
            {"term": "IMPACT", "definition": "The strong effect or influence of the topic.", "length_category": "short", "char_count": 6, "term_no_spaces": "IMPACT"},
            {"term": "CULTURE", "definition": "The customs, arts, and social institutions related to the topic.", "length_category": "short", "char_count": 7, "term_no_spaces": "CULTURE"},
            {"term": "TRADITION", "definition": "A belief or behavior passed down within a group or society related to the topic.", "length_category": "medium", "char_count": 9, "term_no_spaces": "TRADITION"},
            {"term": "EVOLUTION", "definition": "The gradual development or changes related to the topic over time.", "length_category": "medium", "char_count": 9, "term_no_spaces": "EVOLUTION"},
            {"term": "FOUNDATION", "definition": "The basis or groundwork of the topic.", "length_category": "medium", "char_count": 10, "term_no_spaces": "FOUNDATION"},
            {"term": "INSPIRATION", "definition": "The process of being mentally stimulated by the topic.", "length_category": "long", "char_count": 11, "term_no_spaces": "INSPIRATION"},
            {"term": "TRANSFORMATION", "definition": "A thorough or dramatic change related to the topic.", "length_category": "long", "char_count": 14, "term_no_spaces": "TRANSFORMATION"},
            {"term": "CHARACTERISTIC", "definition": "A feature or quality belonging to the topic.", "length_category": "long", "char_count": 14, "term_no_spaces": "CHARACTERISTIC"}
        ]
        return generic_terms[:18]

def prioritize_terms(terms):
    """Prioritize terms based on their content (key people, roles, iconic elements, etc.)."""
    # Define priority categories
    key_people = []
    specific_roles = []
    iconic_elements = []
    concrete_nouns = []
    abstract_concepts = []

    # Keywords that might indicate roles or occupations
    role_keywords = ['LEADER', 'WORKER', 'DRIVER', 'OFFICER', 'TEACHER', 'DOCTOR', 'SCIENTIST',
                    'ARTIST', 'WRITER', 'INVENTOR', 'EXPLORER', 'PRESIDENT', 'KING', 'QUEEN']

    # Keywords that might indicate iconic elements
    iconic_keywords = ['FAMOUS', 'ICONIC', 'SIGNATURE', 'KNOWN FOR', 'SYMBOL', 'LANDMARK', 'MONUMENT',
                      'TRUSTY', 'LOYAL', 'COMPANION', 'SIDEKICK', 'TRADEMARK', 'RECOGNIZABLE', 'ASSOCIATED WITH',
                      'HORSE', 'MASK', 'COSTUME', 'OUTFIT', 'WEAPON', 'VEHICLE', 'CATCHPHRASE']

    # Keywords that might indicate abstract concepts
    abstract_keywords = ['CONCEPT', 'THEORY', 'PHILOSOPHY', 'IDEOLOGY', 'MOVEMENT', 'PRINCIPLE', 'BELIEF']

    for term in terms:
        definition = term.get('definition', '').upper()
        term_text = term.get('term', '').upper()

        # Check if it's a person's name
        if ('PERSON' in definition or 'INDIVIDUAL' in definition or
            'BORN' in definition or 'DIED' in definition or
            'FIGURE' in definition or 'LEADER' in definition or
            'CHARACTER' in definition or 'PLAYED BY' in definition or
            'PORTRAYED' in definition or 'ACTOR' in definition or
            'ACTRESS' in definition):
            # Check if it's a first name (single word, 4+ characters)
            if ' ' not in term_text and len(term_text) >= 4:
                # Prioritize first names even higher
                key_people.insert(0, term)
            else:
                key_people.append(term)
        # Check if it's a specific role or occupation
        elif any(keyword in term_text for keyword in role_keywords) or \
             any(keyword in definition for keyword in role_keywords):
            specific_roles.append(term)
        # Check if it's an iconic element
        elif any(keyword in term_text for keyword in iconic_keywords) or \
             any(keyword in definition for keyword in iconic_keywords) or \
             'HORSE' in term_text or 'MASK' in term_text or 'COSTUME' in term_text or \
             'VEHICLE' in term_text or 'WEAPON' in term_text or 'CATCHPHRASE' in term_text or \
             'COMPANION' in term_text or 'SIDEKICK' in term_text:
            # Prioritize certain iconic elements even higher
            if 'HORSE' in term_text or 'COMPANION' in term_text or 'SIDEKICK' in term_text or 'CATCHPHRASE' in term_text:
                iconic_elements.insert(0, term)
            else:
                iconic_elements.append(term)
        # Check if it's an abstract concept
        elif any(keyword in definition for keyword in abstract_keywords):
            abstract_concepts.append(term)
        # Otherwise, assume it's a concrete noun
        else:
            concrete_nouns.append(term)

    # Return terms in priority order
    return key_people + specific_roles + iconic_elements + concrete_nouns + abstract_concepts

def select_best_terms(long_terms, medium_terms, short_terms):
    """Select the best 18 terms from the filtered lists."""
    # Prioritize terms within each length category
    prioritized_long = prioritize_terms(long_terms)
    prioritized_medium = prioritize_terms(medium_terms)
    prioritized_short = prioritize_terms(short_terms)

    selected_terms = []

    # We want a good distribution with no more than 4 long terms,
    # at least 5 medium terms, and at least 5 short terms

    # First, select up to 4 long terms
    selected_long = prioritized_long[:min(4, len(prioritized_long))]
    selected_terms.extend(selected_long)

    # Next, select at least 5 medium terms, more if needed
    min_medium = max(5, 8 - len(selected_terms) - min(5, len(prioritized_short)))
    selected_medium = prioritized_medium[:min(len(prioritized_medium), min_medium)]
    selected_terms.extend(selected_medium)

    # Next, select at least 5 short terms, more if needed
    min_short = max(5, 18 - len(selected_terms))
    selected_short = prioritized_short[:min(len(prioritized_short), min_short)]
    selected_terms.extend(selected_short)

    # If we still need more terms, add more medium terms
    if len(selected_terms) < 18 and len(selected_medium) < len(prioritized_medium):
        additional_medium = prioritized_medium[len(selected_medium):min(len(prioritized_medium), len(selected_medium) + (18 - len(selected_terms)))]
        selected_terms.extend(additional_medium)

    # If we still need more terms, add more long terms (if we didn't use all 6)
    if len(selected_terms) < 18 and len(selected_long) < len(prioritized_long):
        additional_long = prioritized_long[len(selected_long):min(len(prioritized_long), len(selected_long) + (18 - len(selected_terms)))]
        selected_terms.extend(additional_long)

    # If we still don't have 18 terms, try to add more terms from the remaining pool
    if len(selected_terms) < 18:
        print(f"Warning: Only found {len(selected_terms)} terms. Attempting to add more terms from the remaining pool...")

        # Combine all remaining terms
        remaining_long = [term for term in prioritized_long if term not in selected_terms]
        remaining_medium = [term for term in prioritized_medium if term not in selected_terms]
        remaining_short = [term for term in prioritized_short if term not in selected_terms]

        all_remaining = prioritize_terms(remaining_long + remaining_medium + remaining_short)

        # Try to add terms with the same strict similarity check
        for term in all_remaining:
            if len(selected_terms) >= 18:
                break

            # Check if this term is too similar to any already selected term
            is_similar = False
            for existing_term in selected_terms:
                # Keep using the same threshold
                if is_similar_term(term['term'], existing_term['term']):
                    is_similar = True
                    break

            if not is_similar:
                selected_terms.append(term)
                print(f"  Added additional term '{term['term']}'")

    # If we still don't have 18 terms, we'll need to create some generic terms
    if len(selected_terms) < 18:
        print(f"Warning: Still only have {len(selected_terms)} terms. Adding generic terms...")

        # List of generic terms that could apply to most topics
        generic_terms = [
            {"term": "HISTORY", "definition": "The past events related to the topic.", "length_category": "short", "char_count": 7},
            {"term": "LEGACY", "definition": "The lasting impact or influence of the topic.", "length_category": "short", "char_count": 6},
            {"term": "INFLUENCE", "definition": "The effect or impact of the topic on others.", "length_category": "medium", "char_count": 9},
            {"term": "POPULARITY", "definition": "The state of being liked or supported by many people.", "length_category": "medium", "char_count": 10},
            {"term": "SIGNIFICANCE", "definition": "The importance or relevance of the topic.", "length_category": "medium", "char_count": 12},
            {"term": "DEVELOPMENT", "definition": "The process of growth or progress related to the topic.", "length_category": "medium", "char_count": 11},
            {"term": "INNOVATION", "definition": "New ideas or methods introduced by or related to the topic.", "length_category": "medium", "char_count": 10},
            {"term": "ACHIEVEMENT", "definition": "Something accomplished or attained by the topic.", "length_category": "medium", "char_count": 11},
            {"term": "RECOGNITION", "definition": "Acknowledgment or appreciation for the topic.", "length_category": "medium", "char_count": 11},
            {"term": "CONTRIBUTION", "definition": "Something given or provided by the topic.", "length_category": "long", "char_count": 13}
        ]

        # Add generic terms until we have 18
        for term in generic_terms:
            if len(selected_terms) >= 18:
                break

            # Check if this term is too similar to any already selected term
            is_similar = False
            for existing_term in selected_terms:
                if is_similar_term(term['term'], existing_term['term']):
                    is_similar = True
                    break

            if not is_similar:
                # Add non-spaced version
                term['term_no_spaces'] = term['term'].replace(' ', '').replace("'", "")
                selected_terms.append(term)
                print(f"  Added generic term '{term['term']}'")

    # Print the final count
    long_count = sum(1 for term in selected_terms if term.get('length_category') == 'long')
    medium_count = sum(1 for term in selected_terms if term.get('length_category') == 'medium')
    short_count = sum(1 for term in selected_terms if term.get('length_category') == 'short')
    print(f"Selected {len(selected_terms)} terms: {long_count} long, {medium_count} medium, {short_count} short")

    # Ensure we return at most 18 terms
    return selected_terms[:18]


def is_similar_term(term1, term2, threshold=0.85):
    """Check if two terms are very similar using string similarity."""
    # Compare the non-spaced versions to avoid issues with spacing differences
    term1_no_spaces = term1.replace(' ', '').replace("'", "")
    term2_no_spaces = term2.replace(' ', '').replace("'", "")

    # Check for common word stems (e.g., CULTURE and CULTURAL)
    # If one term is a prefix of the other and at least 4 characters long, they're similar
    if len(term1_no_spaces) >= 4 and len(term2_no_spaces) >= 4:
        min_len = min(len(term1_no_spaces), len(term2_no_spaces))
        # Check if one is a prefix of the other with at least 4 characters
        if term1_no_spaces[:min_len] == term2_no_spaces[:min_len] and min_len >= 4:
            # Check if the remaining parts are typical suffixes
            suffixes = ['S', 'ES', 'ED', 'ING', 'LY', 'AL', 'IC', 'ISM', 'IST', 'IZE', 'MENT', 'TION', 'SION', 'ANCE', 'ENCE', 'ITY', 'NESS']
            if len(term1_no_spaces) > min_len:
                suffix1 = term1_no_spaces[min_len:]
                if suffix1.upper() in suffixes:
                    return True
            if len(term2_no_spaces) > min_len:
                suffix2 = term2_no_spaces[min_len:]
                if suffix2.upper() in suffixes:
                    return True

    # If one is a substring of the other and the longer one is at least 50% longer, they're similar
    if (term1_no_spaces in term2_no_spaces and len(term2_no_spaces) >= len(term1_no_spaces) * 1.5) or \
       (term2_no_spaces in term1_no_spaces and len(term1_no_spaces) >= len(term2_no_spaces) * 1.5):
        return True

    # Special case for family names - prioritize variety
    if ' ' in term1 and ' ' in term2:
        # Extract first and last names
        parts1 = term1.split(' ')
        parts2 = term2.split(' ')

        # Get first and last names
        first1 = parts1[0]
        last1 = parts1[-1]
        first2 = parts2[0]
        last2 = parts2[-1]

        # If last names are the same but first names are different, they're similar
        # This helps us avoid having multiple members of the same family
        if last1 == last2 and first1 != first2:
            return True

    # Special case for family name vs. full name (e.g., "ANDERSON" vs "JIM ANDERSON")
    if ' ' in term1 or ' ' in term2:
        # If one term is a single word and appears as the last word in the other term
        if ' ' not in term1 and term1 in term2.split(' ')[-1]:
            return True
        if ' ' not in term2 and term2 in term1.split(' ')[-1]:
            return True

    # Use difflib to calculate string similarity
    similarity = difflib.SequenceMatcher(None, term1_no_spaces, term2_no_spaces).ratio()
    return similarity >= threshold


def simplify_term(term):
    """Simplify a term by removing articles and shortening compound terms."""
    # Convert to uppercase
    term = term.upper()

    # Remove leading articles
    if term.startswith("THE "):
        term = term[4:]
    elif term.startswith("A "):
        term = term[2:]
    elif term.startswith("AN "):
        term = term[3:]

    # Remove trailing "THE"
    if term.endswith(" THE"):
        term = term[:-4]

    # Fix common errors
    if term == "BRASH DRIVER":
        term = "BUS DRIVER"

    # Simplify compound terms with unnecessary adjectives
    simplifications = [
        # Remove unnecessary suffixes
        (" ARCHETYPE", ""),
        (" SERIES", ""),
        (" FAMILY", ""),
        (" TALES", ""),
        (" LEGACY", ""),
        (" CONCEPT", ""),
        (" THEME", ""),
        (" STYLE", ""),

        # Remove unnecessary adjectives (for non-name terms)
        ("TIMELESS ", ""),
        ("TIMLESS ", ""),  # Common misspelling
        ("CLASSIC ", ""),
        ("ICONIC ", ""),
        ("FAMOUS ", ""),
        ("POPULAR ", ""),
        ("TRADITIONAL ", ""),
        ("HISTORICAL ", ""),
        ("CULTURAL ", ""),
        ("SOCIAL ", ""),
        ("POLITICAL ", ""),
        ("ECONOMIC ", ""),
        ("SCIENTIFIC ", ""),
        ("TECHNOLOGICAL ", ""),
        ("EDUCATIONAL ", ""),
        ("RELIGIOUS ", ""),
        ("SPIRITUAL ", ""),
        ("PHILOSOPHICAL ", ""),
        ("PSYCHOLOGICAL ", ""),
        ("EMOTIONAL ", ""),
        ("PHYSICAL ", ""),
        ("NATURAL ", ""),
        ("ENVIRONMENTAL ", ""),
        ("INDUSTRIAL ", ""),
        ("COMMERCIAL ", ""),
        ("FINANCIAL ", ""),
        ("LEGAL ", ""),
        ("MEDICAL ", ""),
        ("ARTISTIC ", ""),
        ("LITERARY ", ""),
        ("MUSICAL ", ""),
        ("THEATRICAL ", ""),
        ("CINEMATIC ", ""),
        ("DRAMATIC ", ""),
        ("COMEDIC ", ""),
        ("HUMOROUS ", ""),
        ("RELATABLE ", ""),
        ("HUMAN ", ""),
        ("UNIVERSAL ", ""),
        ("GLOBAL ", ""),
        ("NATIONAL ", ""),
        ("REGIONAL ", ""),
        ("LOCAL ", ""),
        ("URBAN ", ""),
        ("RURAL ", ""),
        ("MODERN ", ""),
        ("CONTEMPORARY ", ""),
        ("ANCIENT ", ""),
        ("MEDIEVAL ", ""),
        ("RENAISSANCE ", ""),
        ("VICTORIAN ", ""),
        ("COLONIAL ", ""),
        ("IMPERIAL ", ""),
        ("REVOLUTIONARY ", ""),
        ("PROGRESSIVE ", ""),
        ("CONSERVATIVE ", ""),
        ("LIBERAL ", ""),
        ("RADICAL ", ""),
        ("TRADITIONAL ", ""),
        ("CONVENTIONAL ", ""),
        ("INNOVATIVE ", ""),
        ("CREATIVE ", ""),
        ("ORIGINAL ", ""),
        ("UNIQUE ", ""),
        ("DISTINCTIVE ", ""),
        ("SIGNIFICANT ", ""),
        ("IMPORTANT ", ""),
        ("ESSENTIAL ", ""),
        ("FUNDAMENTAL ", ""),
        ("CRITICAL ", ""),
        ("CRUCIAL ", ""),
        ("VITAL ", ""),
        ("NECESSARY ", ""),
        ("REQUIRED ", ""),
        ("MANDATORY ", ""),
        ("OPTIONAL ", ""),
        ("VOLUNTARY ", ""),
        ("COMPULSORY ", ""),
        ("OBLIGATORY ", "")
    ]

    # First check for suffixes
    for suffix, replacement in simplifications[:8]:  # First 8 items are suffixes
        if term.endswith(suffix):
            term = term[:-len(suffix)] + replacement
            break

    # Then check for prefixes, but only for non-name terms
    # Don't apply to terms that might be names (check if it has a space)
    if " " in term and not any(name in term for name in ["BALL", "YOUNG", "MOORE", "ARNAZ", "KRAMDEN", "NORTON", "MEADOWS", "GLEASON", "CARNEY", "RICARDO", "MERTZ", "ANDERSON"]):
        for prefix, replacement in simplifications[8:]:  # Rest are prefixes
            if term.startswith(prefix):
                term = replacement + term[len(prefix):]
                break

    return term

def process_terms(terms, summary_upper=None, summary_words=None):
    """Process extracted terms to add non-spaced versions and validate requirements."""
    processed_terms = []
    seen_terms = set()  # To track terms we've already processed
    family_names = {}  # To track family names we've seen

    print("\nValidating terms...")

    # First pass: identify family names
    for term_obj in terms:
        term = term_obj['term'].upper()
        if ' ' in term:  # If it's a full name
            last_name = term.split(' ')[-1]
            if last_name not in family_names:
                family_names[last_name] = []
            family_names[last_name].append(term)

    # Print family names found
    for last_name, full_names in family_names.items():
        if len(full_names) > 1:
            print(f"  Found family name '{last_name}' with multiple members: {', '.join(full_names)}")

    for term_obj in terms:
        # Get the original term and ensure it's uppercase
        original_term = term_obj['term'].upper()

        # Simplify the term
        simplified_term = simplify_term(original_term)
        if simplified_term != original_term:
            #print(f"  Simplified '{original_term}' to '{simplified_term}'")
            original_term = simplified_term

        # Check if this is a full name with a family name that appears multiple times
        if ' ' in original_term:
            last_name = original_term.split(' ')[-1]
            if last_name in family_names and len(family_names[last_name]) > 1:
                # If we already have a full name with this last name, use just the first name
                full_name_used = False
                for existing_term in processed_terms:
                    if ' ' in existing_term['term'] and existing_term['term'].split(' ')[-1] == last_name:
                        full_name_used = True
                        break

                if full_name_used:
                    # Use just the first name
                    first_name = original_term.split(' ')[0]
                    if len(first_name) >= 4:  # Make sure it's not too short
                        print(f"  Using first name '{first_name}' instead of full name '{original_term}'")
                        original_term = first_name
                        term_obj['term'] = original_term
                    else:
                        print(f"  First name '{first_name}' is too short, keeping full name '{original_term}'")

        # Create non-spaced version
        non_spaced_term = original_term.replace(' ', '')

        # Add both versions to the term object
        term_obj['term'] = original_term
        term_obj['term_no_spaces'] = non_spaced_term

        # Make sure char_count is set
        if 'char_count' not in term_obj:
            term_obj['char_count'] = len(non_spaced_term)

        # Check for exact duplicates
        # this code does nothing
        # if original_term in seen_terms:
           # print(f"  Rejected '{original_term}': Duplicate term")
            #continue

        # Check for very similar terms
        is_similar = False
        for existing_term in processed_terms:
            if is_similar_term(original_term, existing_term['term']):
                #print(f"  Rejected '{original_term}': Too similar to existing term '{existing_term['term']}'")
                is_similar = True
                break

        if is_similar:
            continue

        # Validate the term meets requirements
        if validate_term(term_obj, summary_upper, summary_words):
            processed_terms.append(term_obj)
            seen_terms.add(original_term)

    return processed_terms

def validate_term(term_obj, summary_upper=None, summary_words=None):
    """Validate that a term meets all requirements."""
    term = term_obj['term']

    # Check for numbers
    if any(char.isdigit() for char in term):
        #print(f"  Rejected '{term}': Contains numbers")
        return False

    # Store the original term for summary checking
    original_for_checking = term

    # Clean the term by removing special characters (except spaces and apostrophes)
    cleaned_term = ''
    for char in term:
        if char.isalpha() or char.isspace() or char == "'":
            cleaned_term += char

    if cleaned_term != term:
        #print(f"  Cleaned '{term}' to '{cleaned_term}' (removed special characters)")
        term_obj['term'] = cleaned_term

    # Update the term_no_spaces - remove spaces and apostrophes
    term_obj['term_no_spaces'] = cleaned_term.replace("'", "").replace(" ", "")

    # Recalculate character count (excluding apostrophes and spaces)
    term_obj['char_count'] = len(term_obj['term_no_spaces'])

    # If summary text is provided, check if the term appears in it
    if summary_upper is not None and summary_words is not None:
        # Special handling for hyphenated terms and other special characters
        # First check if the original term (with hyphens) appears in the summary
        original_upper = original_for_checking.upper()
        if original_upper in summary_upper:
            # Original term found in summary, so it's valid
            pass
        else:
            # Try checking the cleaned term
            # For single-word terms, check if they appear in the summary words
            if ' ' not in cleaned_term:
                # Check if the word or parts of hyphenated words appear
                if cleaned_term.upper() not in summary_words:
                    # For hyphenated terms like "THREE-CAMERA", check if the parts appear
                    if '-' in original_for_checking:
                        hyphen_parts = original_for_checking.split('-')
                        if not all(part.upper() in summary_words for part in hyphen_parts if part):
                            print(f"  Rejected '{cleaned_term}': Not found in summary")
                            return False
                    else:
                        print(f"  Rejected '{cleaned_term}': Not found in summary")
                        return False
            # For multi-word terms, check if all words appear in the summary
            # or if the exact phrase appears in the summary
            else:
                term_words = cleaned_term.upper().split()
                # Check if all words in the term appear in the summary
                if not all(word in summary_words for word in term_words):
                    # Check if the exact phrase appears in the summary
                    if cleaned_term.upper() not in summary_upper:
                        print(f"  Rejected '{cleaned_term}': Not found in summary")
                        return False

    # Recategorize based on actual length
    non_spaced_length = len(term_obj['term_no_spaces'])
    old_category = term_obj['length_category']

    # Assign the correct category based on length
    if 13 <= non_spaced_length <= 16:
        new_category = 'long'
    elif 9 <= non_spaced_length <= 12:
        new_category = 'medium'
    elif 4 <= non_spaced_length <= 8:
        new_category = 'short'
    else:
        # Term is too short or too long
        if non_spaced_length < 4:
            print(f"  Rejected '{term}': Too short ({non_spaced_length} chars, minimum is 4)")
            return False
        else:  # non_spaced_length > 16
            print(f"  Rejected '{term}': Too long ({non_spaced_length} chars, maximum is 16)")
            return False

    # Update the category if it changed
    if new_category != old_category:
        #print(f"  Recategorized '{term}' from '{old_category}' to '{new_category}' ({non_spaced_length} chars)")
        term_obj['length_category'] = new_category

    return True


def extract_terms_for_musician(topic, max_retries=3):
    """Extract top songs from a musician using ChatGPT."""
    print(f"Extracting top songs for musician: {topic}")

    for attempt in range(max_retries):
        if attempt > 0:
            print(f"Retry attempt {attempt}/{max_retries-1} for {topic}...")

        prompt = f"""
        Give me the top 40 most famous songs by {topic}.
        I will use these song titles as terms in a word search puzzle.

        I need a wide variety of song titles with different lengths - some short (4-8 characters), some medium (9-12 characters), and some long (13-16 characters).

        Guidelines:
        - Focus on the most famous/popular songs by {topic}
        - Include a mix of older hits and newer popular songs if applicable
        - Provide the titles in ALL CAPS
        - Include a brief description of each song (1-2 sentences about its significance)

        Format the output as a JSON array of objects with the following structure:
        [
            {{
                "term": "SONG TITLE",
                "definition": "Brief description of the song"
            }},
            ...
        ]

        Please provide at least 40 song titles.
        """

        try:
            response = openai.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a music expert who knows all songs by all musicians and bands. You are helping create a word search puzzle using song titles as the hidden words. Provide a diverse list of song titles with varying lengths. Focus on the most iconic, recognizable songs that fans would immediately associate with the artist. For each song, include the title in ALL CAPS and a brief description of its significance."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.4
            )

            print(prompt)

            # Parse the JSON response
            content = response.choices[0].message.content
            # Find the JSON part in the response (in case there's additional text)
            json_start = content.find('[')
            json_end = content.rfind(']') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                terms = json.loads(json_str)

                # Add char_count and length_category fields
                for term in terms:
                    # Make sure term is uppercase
                    if 'term' in term:
                        term['term'] = term['term'].upper()

                        # For song titles only: Remove 'THE ' from the beginning if present
                        # This applies to all terms in the music-related extraction methods
                        if term['term'].startswith('THE '):
                            print(f"  Removing 'THE' from beginning of '{term['term']}'")
                            term['term'] = term['term'][4:]  # Remove 'THE '

                    # Create term_no_spaces by removing spaces and apostrophes
                    # Keep apostrophes in the original term
                    term_no_spaces = term['term']
                    term_no_spaces = term_no_spaces.replace("'", "")  # Remove apostrophes
                    term_no_spaces = term_no_spaces.replace(" ", "")  # Remove spaces
                    term['term_no_spaces'] = term_no_spaces

                    # Calculate character count (excluding apostrophes and spaces)
                    char_count = len(term['term_no_spaces'])
                    term['char_count'] = char_count

                    # Assign length category
                    if 13 <= char_count <= 16:
                        term['length_category'] = 'long'
                    elif 9 <= char_count <= 12:
                        term['length_category'] = 'medium'
                    elif 4 <= char_count <= 8:
                        term['length_category'] = 'short'
                    else:
                        term['length_category'] = 'invalid'

                # Print the initial list of terms (up to 40)
                print("\nInitial list of song titles:")
                for i, term in enumerate(terms[:40]):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({term.get('char_count')} chars)")

                # Process terms to add non-spaced versions and validate
                processed_terms = process_terms(terms)

                # Print the filtered list of terms
                print("\nFiltered list of song titles (after validation):")
                for i, term in enumerate(processed_terms):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({len(term.get('term_no_spaces'))} chars)")

                # Group terms by category
                long_terms = [term for term in processed_terms if term.get('length_category') == 'long']
                medium_terms = [term for term in processed_terms if term.get('length_category') == 'medium']
                short_terms = [term for term in processed_terms if term.get('length_category') == 'short']

                print(f"Filtered to {len(processed_terms)} song titles with distribution: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")

                # Check if we have enough terms in each category
                if len(processed_terms) >= 18 and len(long_terms) <= 4 and len(medium_terms) >= 5 and len(short_terms) >= 5:
                    # Select the best mix of terms
                    selected_terms = select_best_terms(long_terms, medium_terms, short_terms)
                    print(f"Successfully selected 18 terms with distribution: {sum(1 for term in selected_terms if term.get('length_category') == 'long')} long, {sum(1 for term in selected_terms if term.get('length_category') == 'medium')} medium, {sum(1 for term in selected_terms if term.get('length_category') == 'short')} short")
                    return selected_terms
                else:
                    if len(processed_terms) < 18:
                        print(f"Warning: Only got {len(processed_terms)} valid song titles, need at least 18")
                    else:
                        print(f"Warning: Got {len(processed_terms)} terms but distribution is inadequate: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")
                    # Continue to next retry attempt
            else:
                print(f"Error: Could not find valid JSON in the response for {topic}")
        except Exception as e:
            print(f"Error extracting song titles for {topic}: {e}")

    # If we've exhausted all retries, return whatever we have (even if incomplete)
    print(f"Warning: Could not get exactly 18 song titles with correct distribution after {max_retries} attempts")
    return processed_terms if 'processed_terms' in locals() else []


def extract_terms_for_year(topic, max_retries=3):
    """Extract top songs from a specific year using ChatGPT."""
    print(f"Extracting top songs from year: {topic}")

    for attempt in range(max_retries):
        if attempt > 0:
            print(f"Retry attempt {attempt}/{max_retries-1} for {topic}...")

        prompt = f"""
        Give me the top 30 most popular songs from the year {topic}.
        I will use these song titles as terms in a word search puzzle. The titles must follow these specific requirements:

        Length requirements:
        - LONG titles: 13-16 characters (without spaces)
        - MEDIUM titles: 9-12 characters (without spaces)
        - SHORT titles: 4-8 characters (without spaces)
        - Provide a good mix of lengths, with more medium and short titles than long ones

        Content requirements:
        - Use ONLY the most popular/chart-topping songs from {topic}
        - Include songs from different genres for variety
        - NO numbers (e.g., no "Summer of '69" or "24K Magic")
        - Special characters removed (e.g., "Don't Stop Believin'" becomes "DONT STOP BELIEVIN)        - NO duplicate words across different songs
        - If a song has a subtitle, OMIT it (e.g., use "THRILLER" not "THRILLER - SINGLE VERSION")
        - Character count is calculated WITHOUT spaces (e.g., "SONG TITLE" is 9 characters, not 10)
        - ALL CAPS for all titles

        For each song title, provide:
        1. The title in ALL CAPS
        2. The artist/band name and a brief description of the song's significance in {topic}
        3. The length category ("long", "medium", or "short")
        4. Character count (excluding spaces)

        Format the output as a JSON array of objects with the following structure:
        [
            {{
                "term": "SONGS TITLE",
                "definition": "Brief description of the song",
                "length_category": "medium",
                "char_count": 10
            }},
            ...
        ]

        CRITICAL: You MUST provide EXACTLY 18 song titles total, with NO MORE THAN 4 long titles, AT LEAST 5 medium titles, and AT LEAST 5 short titles. Count carefully!
        If you can't find enough songs in a category, you can modify titles slightly by removing articles or combining titles.
        """

        try:
            response = openai.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a music historian who knows all popular songs from every year. In this context, 'term' and 'song title' are interchangeable - we are using song titles as terms for a word search puzzle. You follow guidelines EXACTLY, especially regarding term counts and length categories. You MUST provide EXACTLY 18 terms (song titles) total, with NO MORE THAN 4 long terms, AT LEAST 5 medium terms, and AT LEAST 5 short terms. You are meticulous about character counts and categorization."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.4
            )

            # Parse the JSON response
            content = response.choices[0].message.content
            # Find the JSON part in the response (in case there's additional text)
            json_start = content.find('[')
            json_end = content.rfind(']') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                terms = json.loads(json_str)

                # Print the initial list of terms (up to 30)
                print("\nInitial list of song titles:")
                for i, term in enumerate(terms[:30]):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({term.get('char_count')} chars)")

                # Process terms to add non-spaced versions and validate
                processed_terms = process_terms(terms)

                # Print the filtered list of terms
                print("\nFiltered list of song titles (after validation):")
                for i, term in enumerate(processed_terms):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({len(term.get('term_no_spaces'))} chars)")

                # Group terms by category
                long_terms = [term for term in processed_terms if term.get('length_category') == 'long']
                medium_terms = [term for term in processed_terms if term.get('length_category') == 'medium']
                short_terms = [term for term in processed_terms if term.get('length_category') == 'short']

                print(f"Filtered to {len(processed_terms)} song titles with distribution: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")

                # Check if we have enough terms in each category
                if len(processed_terms) >= 18 and len(long_terms) <= 4 and len(medium_terms) >= 5 and len(short_terms) >= 5:
                    # Select the best mix of terms
                    selected_terms = select_best_terms(long_terms, medium_terms, short_terms)
                    print(f"Successfully selected 18 terms with distribution: {sum(1 for term in selected_terms if term.get('length_category') == 'long')} long, {sum(1 for term in selected_terms if term.get('length_category') == 'medium')} medium, {sum(1 for term in selected_terms if term.get('length_category') == 'short')} short")
                    return selected_terms
                else:
                    if len(processed_terms) < 18:
                        print(f"Warning: Only got {len(processed_terms)} valid song titles, need at least 18")
                    else:
                        print(f"Warning: Got {len(processed_terms)} terms but distribution is inadequate: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")
                    # Continue to next retry attempt
            else:
                print(f"Error: Could not find valid JSON in the response for {topic}")
        except Exception as e:
            print(f"Error extracting song titles for {topic}: {e}")

    # If we've exhausted all retries, return whatever we have (even if incomplete)
    print(f"Warning: Could not get exactly 18 song titles with correct distribution after {max_retries} attempts")
    return processed_terms if 'processed_terms' in locals() else []


def extract_terms_for_genre(topic, max_retries=3):
    """Extract top songs from a specific genre using ChatGPT."""
    print(f"Extracting top songs from genre: {topic}")

    for attempt in range(max_retries):
        if attempt > 0:
            print(f"Retry attempt {attempt}/{max_retries-1} for {topic}...")

        prompt = f"""
        Give me the top 30 most iconic songs from the {topic} genre.
        I will use these song titles as terms in a word search puzzle. The titles must follow these specific requirements:

        Length requirements:
        - LONG titles: 13-16 characters (without spaces)
        - MEDIUM titles: 9-12 characters (without spaces)
        - SHORT titles: 4-8 characters (without spaces)
        - Provide a good mix of lengths, with more medium and short titles than long ones

        Content requirements:
        - Use ONLY the most iconic/influential songs in the {topic} genre
        - Include songs from different time periods for variety
        - NO numbers (e.g., no "99 Problems" or "21 Questions")
        - Special characters removed (e.g., "Don't Stop Believin'" becomes "DONT STOP BELIEVIN)        - NO duplicate words across different songs
        - NO duplicate words across different songs
        - If a song has a subtitle, OMIT it (e.g., use "THRILLER" not "THRILLER - SINGLE VERSION", use "WILD BOYS" not "WILD BOYS (REMIX)")
        - Character count is calculated WITHOUT spaces (e.g., "SONG TITLE" is 9 characters, not 10)
        - ALL CAPS for all titles

        For each song title, provide:
        1. The title in ALL CAPS
        2. The artist/band name and why this song is significant in the {topic} genre
        3. The length category ("long", "medium", or "short")
        4. Character count (excluding spaces)

        Format the output as a JSON array of objects with the following structure:
        [
            {{
                "term": "SONGS TITLE",
                "definition": "Brief description of the song",
                "length_category": "medium",
                "char_count": 10
            }},
            ...
        ]

        CRITICAL: You MUST provide EXACTLY 18 song titles total, with NO MORE THAN 4 long titles, AT LEAST 5 medium titles, and AT LEAST 5 short titles. Count carefully!
        If you can't find enough songs in a category, you can modify titles slightly by removing articles or combining titles.
        """

        try:
            response = openai.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a music expert who knows all songs from every genre. In this context, 'term' and 'song title' are interchangeable - we are using song titles as terms for a word search puzzle. You follow guidelines EXACTLY, especially regarding term counts and length categories. You MUST provide EXACTLY 18 terms (song titles) total, with NO MORE THAN 4 long terms, AT LEAST 5 medium terms, and AT LEAST 5 short terms. You are meticulous about character counts and categorization."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.4
            )

            # Parse the JSON response
            content = response.choices[0].message.content
            # Find the JSON part in the response (in case there's additional text)
            json_start = content.find('[')
            json_end = content.rfind(']') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                terms = json.loads(json_str)

                # Print the initial list of terms (up to 30)
                print("\nInitial list of song titles:")
                for i, term in enumerate(terms[:30]):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({term.get('char_count')} chars)")

                # Process terms to add non-spaced versions and validate
                processed_terms = process_terms(terms)

                # Print the filtered list of terms
                print("\nFiltered list of song titles (after validation):")
                for i, term in enumerate(processed_terms):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({len(term.get('term_no_spaces'))} chars)")

                # Group terms by category
                long_terms = [term for term in processed_terms if term.get('length_category') == 'long']
                medium_terms = [term for term in processed_terms if term.get('length_category') == 'medium']
                short_terms = [term for term in processed_terms if term.get('length_category') == 'short']

                print(f"Filtered to {len(processed_terms)} song titles with distribution: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")

                # Check if we have enough terms in each category
                if len(processed_terms) >= 18 and len(long_terms) <= 4 and len(medium_terms) >= 5 and len(short_terms) >= 5:
                    # Select the best mix of terms
                    selected_terms = select_best_terms(long_terms, medium_terms, short_terms)
                    print(f"Successfully selected 18 terms with distribution: {sum(1 for term in selected_terms if term.get('length_category') == 'long')} long, {sum(1 for term in selected_terms if term.get('length_category') == 'medium')} medium, {sum(1 for term in selected_terms if term.get('length_category') == 'short')} short")
                    return selected_terms
                else:
                    if len(processed_terms) < 18:
                        print(f"Warning: Only got {len(processed_terms)} valid song titles, need at least 18")
                    else:
                        print(f"Warning: Got {len(processed_terms)} terms but distribution is inadequate: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")
                    # Continue to next retry attempt
            else:
                print(f"Error: Could not find valid JSON in the response for {topic}")
        except Exception as e:
            print(f"Error extracting song titles for {topic}: {e}")

    # If we've exhausted all retries, return whatever we have (even if incomplete)
    print(f"Warning: Could not get exactly 18 song titles with correct distribution after {max_retries} attempts")
    return processed_terms if 'processed_terms' in locals() else []

def extract_terms_for_topic (topic, max_retries=3):
    """Extract {topic} using ChatGPT."""
    print(f"Extracting terms for: {topic}")

    for attempt in range(max_retries):
        if attempt > 0:
            print(f"Retry attempt {attempt}/{max_retries-1} for {topic}...")

        prompt = f"""
        Give me the top 20-30 {topic}, I may refer to these as 'terms'.
        I will use this list of {topic} as terms in a word search puzzle.

        I need a wide variety of {topic} with different lengths - some short (4-8 characters), some medium (9-12 characters), and some long (13-16 characters).

        Guidelines:
        - Focus on the most well-known/popular {topic}
        - Provide the terms in ALL CAPS
        - Include a brief description of each (1-2 sentences about why it's included in {topic})

        Format the output as a JSON array of objects with the following structure:
        [
            {{
                "term": "TERM IN ALL CAPS",
                "definition": "Brief description of why it's included in {topic}"
            }},
            ...
        ]

        Please provide at least 20-30 {topic}.
        """

        print("DOG BREED Prompt: " + prompt)

        try:
            response = openai.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are an expert on {topic}. You are helping create a word search puzzle using {topic} as the hidden words. Provide a diverse list of {topic} with varying lengths. Include each of the {topic} in ALL CAPS and a brief description of its significance."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=3000,
                temperature=0.4
            )

            # Parse the JSON response
            content = response.choices[0].message.content
            # Find the JSON part in the response (in case there's additional text)
            json_start = content.find('[')
            json_end = content.rfind(']') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                terms = json.loads(json_str)

                # Add char_count and length_category fields
                for term in terms:
                    # Make sure term is uppercase
                    if 'term' in term:
                        term['term'] = term['term'].upper()

                    # Create term_no_spaces by removing spaces and apostrophes
                    # Keep apostrophes in the original term
                    term_no_spaces = term['term']
                    term_no_spaces = term_no_spaces.replace("'", "")  # Remove apostrophes
                    term_no_spaces = term_no_spaces.replace(" ", "")  # Remove spaces
                    term['term_no_spaces'] = term_no_spaces

                    # Calculate character count (excluding apostrophes and spaces)
                    char_count = len(term['term_no_spaces'])
                    term['char_count'] = char_count

                    # Assign length category
                    if 13 <= char_count <= 16:
                        term['length_category'] = 'long'
                    elif 9 <= char_count <= 12:
                        term['length_category'] = 'medium'
                    elif 4 <= char_count <= 8:
                        term['length_category'] = 'short'
                    else:
                        term['length_category'] = 'invalid'

                # Print the initial list of terms (up to 30)
                print("\nInitial list of song titles:")
                for i, term in enumerate(terms[:30]):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({term.get('char_count')} chars)")

                # Process terms to add non-spaced versions and validate
                processed_terms = process_terms(terms)

                # Print the filtered list of terms
                print("\nFiltered list of {topic} (after validation):")
                for i, term in enumerate(processed_terms):
                    print(f"  {i+1}. {term.get('term')} - {term.get('length_category')} ({len(term.get('term_no_spaces'))} chars)")

                # Group terms by category
                long_terms = [term for term in processed_terms if term.get('length_category') == 'long']
                medium_terms = [term for term in processed_terms if term.get('length_category') == 'medium']
                short_terms = [term for term in processed_terms if term.get('length_category') == 'short']

                print(f"Filtered to {len(processed_terms)} {topic} with distribution: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")

                # Check if we have enough terms in each category
                if len(processed_terms) >= 18 and len(long_terms) <= 4 and len(medium_terms) >= 5 and len(short_terms) >= 5:
                    # Select the best mix of terms
                    selected_terms = select_best_terms(long_terms, medium_terms, short_terms)
                    print(f"Successfully selected 18 terms with distribution: {sum(1 for term in selected_terms if term.get('length_category') == 'long')} long, {sum(1 for term in selected_terms if term.get('length_category') == 'medium')} medium, {sum(1 for term in selected_terms if term.get('length_category') == 'short')} short")
                    return selected_terms
                else:
                    if len(processed_terms) < 18:
                        print(f"Warning: Only got {len(processed_terms)} valid {topic}, need at least 18")
                    else:
                        print(f"Warning: Got {len(processed_terms)} terms but distribution is inadequate: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")
                    # Continue to next retry attempt
            else:
                print(f"Error: Could not find valid JSON in the response for {topic}")
        except Exception as e:
            print(f"Error extracting {topic}: {e}")

    # If we've exhausted all retries, return whatever we have (even if incomplete)
    print(f"Warning: Could not get exactly 18 {topic} with correct distribution after {max_retries} attempts")
    return processed_terms if 'processed_terms' in locals() else []


def load_additional_terms():
    """Load additional terms from the JSON file."""
    additional_terms_file = Path(__file__).resolve().parents[1] / 'additional_terms.json'
    if additional_terms_file.exists():
        try:
            with open(additional_terms_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading additional terms: {e}")
    return {}
def adjust_term_distribution(terms):
    """Adjust the distribution of terms to meet requirements without a full retry.

    Requirements:
    - No more than 4 long terms (13-16 characters)
    - At least 5 medium terms (9-12 characters)
    - At least 5 short terms (4-8 characters)
    - Total of 18 terms
    """
    # Group terms by category
    long_terms = [term for term in terms if term.get('length_category') == 'long']
    medium_terms = [term for term in terms if term.get('length_category') == 'medium']
    short_terms = [term for term in terms if term.get('length_category') == 'short']

    print(f"Current distribution: {len(long_terms)} long, {len(medium_terms)} medium, {len(short_terms)} short")

    # Check if we need to adjust
    if len(long_terms) <= 4 and len(medium_terms) >= 5 and len(short_terms) >= 5:
        # Distribution is already good, just select the best 18 terms
        return select_best_terms(long_terms, medium_terms, short_terms)

    # Prioritize terms within each category
    prioritized_long = prioritize_terms(long_terms)
    prioritized_medium = prioritize_terms(medium_terms)
    prioritized_short = prioritize_terms(short_terms)

    # Determine what adjustments are needed
    need_more_medium = len(medium_terms) < 5
    need_more_short = len(short_terms) < 5

    # Make adjustments
    selected_terms = []

    # 1. Select long terms (up to 4)
    selected_long = prioritized_long[:min(4, len(prioritized_long))]
    selected_terms.extend(selected_long)

    # 2. Select medium terms (at least 5)
    # If we need more medium terms but have too many long terms, we'll need to add generic medium terms
    if need_more_medium:
        print(f"Need more medium terms (have {len(medium_terms)}, need at least 5)")
        selected_medium = prioritized_medium
        selected_terms.extend(selected_medium)

        # Add generic medium terms if needed
        generic_medium_terms = [
        ]

        # Add generic medium terms until we have at least 5
        for term in generic_medium_terms:
            if len(selected_medium) >= 5:
                break

            # Check if this term is too similar to any already selected term
            is_similar = False
            for existing_term in selected_terms:
                if is_similar_term(term['term'], existing_term['term']):
                    is_similar = True
                    break

            if not is_similar:
                selected_medium.append(term)
                selected_terms.append(term)
                print(f"  Added generic medium term: {term['term']}")
    else:
        # We have enough medium terms, select at least 5
        selected_medium = prioritized_medium[:max(5, 18 - len(selected_long) - min(5, len(prioritized_short)))]
        selected_terms.extend(selected_medium)

    # 3. Select short terms (at least 5)
    # If we need more short terms but have too many long terms, we'll need to add generic short terms
    if need_more_short:
        print(f"Need more short terms (have {len(short_terms)}, need at least 5)")
        selected_short = prioritized_short
        selected_terms.extend(selected_short)

        # Add generic short terms if needed
        generic_short_terms = [
        ]

        # Add generic short terms until we have at least 5
        for term in generic_short_terms:
            if len(selected_short) >= 5:
                break

            # Check if this term is too similar to any already selected term
            is_similar = False
            for existing_term in selected_terms:
                if is_similar_term(term['term'], existing_term['term']):
                    is_similar = True
                    break

            if not is_similar:
                selected_short.append(term)
                selected_terms.append(term)
                print(f"  Added generic short term: {term['term']}")
    else:
        # We have enough short terms, select at least 5
        selected_short = prioritized_short[:max(5, 18 - len(selected_terms))]
        selected_terms.extend(selected_short)

    # 4. If we still need more terms to reach 18, add more from any category
    if len(selected_terms) < 18:
        # Combine all remaining terms
        remaining_long = [term for term in prioritized_long if term not in selected_terms]
        remaining_medium = [term for term in prioritized_medium if term not in selected_terms]
        remaining_short = [term for term in prioritized_short if term not in selected_terms]

        all_remaining = prioritize_terms(remaining_medium + remaining_short + remaining_long)

        # Add remaining terms until we have 18
        for term in all_remaining:
            if len(selected_terms) >= 18:
                break

            # Check if this term is too similar to any already selected term
            is_similar = False
            for existing_term in selected_terms:
                if is_similar_term(term['term'], existing_term['term']):
                    is_similar = True
                    break

            if not is_similar:
                selected_terms.append(term)
                print(f"  Added additional term: {term['term']}")

    # Ensure we have exactly 18 terms
    return selected_terms[:18]

def supplement_terms(terms, topic, target_count=18):
    """Supplement the extracted terms with additional terms to reach the target count."""
    if len(terms) >= target_count:
        return terms[:target_count]  # If we already have enough terms, just return the first target_count

    # Load additional terms
    all_additional_terms = load_additional_terms()
    additional_terms = all_additional_terms.get(topic, [])

    if not additional_terms:
        print(f"No additional terms found for topic: {topic}")
        return terms

    # Get the terms we already have
    existing_term_texts = [term['term'] for term in terms]

    # Filter out additional terms that we already have
    filtered_additional_terms = [term for term in additional_terms if term['term'] not in existing_term_texts]

    # Shuffle the additional terms to get a random selection
    random.shuffle(filtered_additional_terms)

    # Add additional terms until we reach the target count or run out of additional terms
    while len(terms) < target_count and filtered_additional_terms:
        terms.append(filtered_additional_terms.pop(0))

    return terms



def main(override_method=None):
    """Main function to extract terms from summaries.

    Args:
        override_method: If provided, override the term extraction method for all topics.
    """
    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = base_dir / 'output'
    summary_output_dir = SUMMARY_DIR / 'output'

    # Ensure directories exist
    ensure_dir(input_dir)
    ensure_dir(output_dir)

    # Print configuration information
    print("Term extraction configuration:")
    if override_method:
        print(f"  Using command-line override method: {override_method} for all topics")
    else:
        print(f"  Using default method: default for all topics")

    # Check if we need to copy summary files from Step 1
    summary_files = get_summary_files(input_dir)
    if not summary_files:
        print(f"No summary files found in {input_dir}")
        print(f"Copying summary files from {summary_output_dir}...")

        # Copy all markdown files from the summary output directory
        if summary_output_dir.exists():
            copied = copy_files(summary_output_dir, input_dir, "*.md")
            print(f"Copied {len(copied)} summary files to {input_dir}")
        else:
            print(f"Warning: Summary output directory {summary_output_dir} not found")
            print("Please run Step 1 (Summary Generation) first")
            return

        # Get the summary files again after copying
        summary_files = get_summary_files(input_dir)
        if not summary_files:
            print(f"Still no summary files found in {input_dir} after copying")
            return

    print(f"Found {len(summary_files)} summary files to process")

    # Process each summary file
    for summary_file in summary_files:
        # Get the file stem for output naming
        file_stem = summary_file.stem

        # Extract content and metadata from the markdown file
        frontmatter, summary_text = extract_content_from_markdown(summary_file)

        # Get the topic name from frontmatter or filename
        if frontmatter and 'title' in frontmatter:
            topic = frontmatter['title']
        else:
            topic = file_stem.replace('_', ' ').title()

        # Determine the method to use
        if override_method:
            method = override_method
            print(f"Using override method for {topic}: {method}")
        else:
            # Always use the default method when no override is provided
            method = "default"
            print(f"Using default method for {topic}: {method}")

        if method == "chatgpt_musician" or method == "chatgpt_year" or method == "chatgpt_genre":
            print(f"Extracting song titles for: {topic} using method: {method}")
        elif method == "chatgpt_topic":
            print(f"Extracting terms for: {topic} using method: {method}")
        else:
            print(f"Extracting terms from: {topic} using method: {method}")

        # Extract terms or song titles based on the method
        if method == "chatgpt_musician":
            terms = extract_terms_for_musician(topic)
        elif method == "chatgpt_year":
            terms = extract_terms_for_year(topic)
        elif method == "chatgpt_genre":
            terms = extract_terms_for_genre(topic)
        elif method == "chatgpt_topic":
            terms = extract_terms_for_topic(topic)
        else:
            # Use the default extraction method
            terms = extract_terms_from_summary(summary_text, topic)

        if terms:
            # Supplement terms if we don't have enough
            if len(terms) < 18:
                if method == "chatgpt_musician" or method == "chatgpt_year" or method == "chatgpt_genre":
                    print(f"Only extracted {len(terms)} song titles. Supplementing with additional terms...")
                else:
                    print(f"Only extracted {len(terms)} terms. Supplementing with additional terms...")
                terms = supplement_terms(terms, topic)

            # Count terms by length category
            long_terms = sum(1 for term in terms if term.get('length_category') == 'long')
            medium_terms = sum(1 for term in terms if term.get('length_category') == 'medium')
            short_terms = sum(1 for term in terms if term.get('length_category') == 'short')

            # Add metadata to the terms output
            terms_with_metadata = {
                "topic": topic,
                "source_file": str(summary_file.name),
                "extraction_date": datetime.now().isoformat(),
                "term_count": len(terms),
                "long_terms": long_terms,
                "medium_terms": medium_terms,
                "short_terms": short_terms,
                "terms": terms
            }

            # Save terms to JSON file
            output_file = output_dir / f"{file_stem}_terms.json"
            write_json_file(output_file, terms_with_metadata)
            print(f"Final: {len(terms)} terms for '{topic}' ({long_terms} long, {medium_terms} medium, {short_terms} short)")
            # Print all terms on one line
            term_names = [term_obj.get('term', 'Unknown') for term_obj in terms]
            print(f"All terms: {', '.join(term_names)}")

        else:
            print(f"No terms extracted for '{topic}'. Using only additional terms.")
            terms = supplement_terms([], topic)
            if terms:
                # Count terms by length category
                long_terms = sum(1 for term in terms if term.get('length_category') == 'long')
                medium_terms = sum(1 for term in terms if term.get('length_category') == 'medium')
                short_terms = sum(1 for term in terms if term.get('length_category') == 'short')

                # Add metadata to the terms output
                terms_with_metadata = {
                    "topic": topic,
                    "source_file": str(summary_file.name),
                    "extraction_date": datetime.now().isoformat(),
                    "term_count": len(terms),
                    "long_terms": long_terms,
                    "medium_terms": medium_terms,
                    "short_terms": short_terms,
                    "terms": terms
                }

                # Save terms to JSON file
                output_file = output_dir / f"{file_stem}_terms.json"
                write_json_file(output_file, terms_with_metadata)
                print(f"Using {len(terms)} additional terms for '{topic}'")
                # Print all terms on one line
                term_names = [term_obj.get('term', 'Unknown') for term_obj in terms]
                print(f"All terms: {', '.join(term_names)}")
                print(f"Saved to {output_file}")
            else:
                print(f"No additional terms found for '{topic}'. Please check the summary content.")

    print("Term extraction complete!")

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract terms from summaries")
    parser.add_argument(
        "--method",
        choices=["default", "chatgpt_musician", "chatgpt_year", "chatgpt_genre", "chatgpt_topic"],
        help="Override the term extraction method for all topics"
    )
    args = parser.parse_args()

    # Call main with the override method if specified
    main(override_method=args.method)
