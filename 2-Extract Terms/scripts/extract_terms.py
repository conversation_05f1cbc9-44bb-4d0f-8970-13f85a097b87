#!/usr/bin/env python3
"""
Number Extraction Script Wrapper

This script is a wrapper that calls the extract_numbers.py script for number extraction
from summaries in the Number Search Book Generation project.
"""

import sys
import subprocess
from pathlib import Path

def main(override_method=None):
    """Main function that calls the extract_numbers.py script."""
    
    # Get the path to the extract_numbers.py script
    script_dir = Path(__file__).parent
    extract_numbers_script = script_dir / "extract_numbers.py"
    
    if not extract_numbers_script.exists():
        print(f"Error: extract_numbers.py not found at {extract_numbers_script}")
        return
    
    print("Number Search Book Generation - Term Extraction")
    print("=" * 50)
    print("Calling extract_numbers.py for number extraction...")
    print()
    
    # Call the extract_numbers.py script
    try:
        result = subprocess.run([
            sys.executable, 
            str(extract_numbers_script)
        ], check=True, capture_output=False)
        
        print("\nNumber extraction completed successfully!")
        
    except subprocess.CalledProcessError as e:
        print(f"Error running extract_numbers.py: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract numbers from summaries")
    parser.add_argument(
        "--method",
        help="Method argument (ignored - kept for compatibility with existing workflow)"
    )
    args = parser.parse_args()

    # Call main (method argument is ignored since we only do number extraction)
    main()
