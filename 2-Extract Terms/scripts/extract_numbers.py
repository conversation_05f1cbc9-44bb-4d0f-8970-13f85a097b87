#!/usr/bin/env python3
"""
Number Extraction Script

This script extracts numbers that are 4+ digits long from text files,
removing commas and other formatting. For example, "2,403" becomes "2403".
"""

import sys
import json
import re
import yaml
from pathlib import Path
from datetime import datetime
from collections import Counter

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir, read_markdown_file, write_json_file, copy_files
from common.config import SUMMARY_DIR

def get_summary_files(input_dir):
    """Get all markdown summary files from the input directory."""
    return list(Path(input_dir).glob('*.md'))

def extract_content_from_markdown(file_path):
    """Extract content and metadata from a markdown file with YAML frontmatter."""
    content = read_markdown_file(file_path)

    # Extract YAML frontmatter if present
    yaml_pattern = re.compile(r'^---\s*\n(.*?)\n---\s*\n', re.DOTALL)
    match = yaml_pattern.match(content)

    if match:
        try:
            # Extract and parse the YAML frontmatter
            frontmatter = yaml.safe_load(match.group(1))
            # Get the content after the frontmatter
            markdown_content = content[match.end():]
            return frontmatter, markdown_content
        except yaml.YAMLError as e:
            print(f"Error parsing YAML frontmatter: {e}")

    # If no frontmatter or error parsing, return empty dict and full content
    return {}, content

def extract_numbers_from_text(text):
    """Extract numbers that are 4+ digits long from text, removing commas."""
    # Pattern to match numbers with optional commas
    # This will match:
    # - 1,234 -> 1234
    # - 12,345 -> 12345
    # - 123,456,789 -> 123456789
    # - 1234 -> 1234
    # But not:
    # - 123 (too short)
    # - 12.34 (decimal numbers)
    # - 1,23 (invalid comma placement)
    
    # Find all potential number patterns
    # This pattern looks for sequences of digits with optional commas
    # that follow proper comma placement rules (every 3 digits from the right)
    number_pattern = r'\b\d{1,3}(?:,\d{3})*\b|\b\d{4,}\b'
    
    matches = re.findall(number_pattern, text)
    
    extracted_numbers = []
    
    for match in matches:
        # Remove commas
        clean_number = match.replace(',', '')
        
        # Only keep numbers that are 4+ digits long
        if len(clean_number) >= 4 and clean_number.isdigit():
            extracted_numbers.append({
                'original': match,
                'cleaned': clean_number,
                'length': len(clean_number)
            })
    
    return extracted_numbers

def categorize_numbers_by_length(numbers):
    """Categorize numbers by their length for word search puzzle generation."""
    categorized = {
        'short': [],      # 4-8 digits
        'medium': [],     # 9-12 digits  
        'long': []        # 13+ digits
    }
    
    for num_data in numbers:
        length = num_data['length']
        if 4 <= length <= 8:
            categorized['short'].append(num_data)
        elif 9 <= length <= 12:
            categorized['medium'].append(num_data)
        elif length >= 13:
            categorized['long'].append(num_data)
    
    return categorized

def extract_numbers_from_summary(summary_text, topic):
    """Extract numbers from a summary text."""
    print(f"\nExtracting numbers from: {topic}")
    
    # Extract all numbers
    numbers = extract_numbers_from_text(summary_text)
    
    print(f"Found {len(numbers)} numbers (4+ digits)")
    
    # Show some examples
    if numbers:
        print("Examples:")
        for i, num_data in enumerate(numbers[:10]):  # Show first 10
            print(f"  {num_data['original']} -> {num_data['cleaned']} ({num_data['length']} digits)")
        if len(numbers) > 10:
            print(f"  ... and {len(numbers) - 10} more")
    
    # Count occurrences of each number
    number_counter = Counter([num_data['cleaned'] for num_data in numbers])
    
    # Create unique number list with frequency data
    unique_numbers = []
    seen_numbers = set()
    
    for num_data in numbers:
        cleaned = num_data['cleaned']
        if cleaned not in seen_numbers:
            unique_numbers.append({
                'number': cleaned,
                'original_format': num_data['original'],
                'length': num_data['length'],
                'frequency': number_counter[cleaned]
            })
            seen_numbers.add(cleaned)
    
    # Sort by frequency (most frequent first)
    unique_numbers.sort(key=lambda x: x['frequency'], reverse=True)
    
    # Categorize by length
    categorized = categorize_numbers_by_length(unique_numbers)
    
    print(f"Unique numbers: {len(unique_numbers)}")
    print(f"  Short (4-8 digits): {len(categorized['short'])}")
    print(f"  Medium (9-12 digits): {len(categorized['medium'])}")
    print(f"  Long (13+ digits): {len(categorized['long'])}")
    
    return unique_numbers, categorized

def main():
    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = base_dir / 'output'
    summary_output_dir = SUMMARY_DIR / 'output'

    # Ensure directories exist
    ensure_dir(input_dir)
    ensure_dir(output_dir)

    # Check if we need to copy summary files from Step 1
    summary_files = get_summary_files(input_dir)
    if not summary_files:
        print(f"No summary files found in {input_dir}")
        print(f"Copying summary files from {summary_output_dir}...")

        # Copy all markdown files from the summary output directory
        if summary_output_dir.exists():
            copied = copy_files(summary_output_dir, input_dir, "*.md")
            print(f"Copied {len(copied)} summary files to {input_dir}")
        else:
            print(f"Warning: Summary output directory {summary_output_dir} not found")
            print("Please run Step 1 (Summary Generation) first")
            return

        # Get the summary files again after copying
        summary_files = get_summary_files(input_dir)
        if not summary_files:
            print(f"Still no summary files found in {input_dir} after copying")
            return

    print(f"Found {len(summary_files)} summary files to process")

    # Process each summary file
    for summary_file in summary_files:
        # Get the file stem for output naming
        file_stem = summary_file.stem

        # Extract content and metadata from the markdown file
        frontmatter, summary_text = extract_content_from_markdown(summary_file)

        # Get the topic name from frontmatter or filename
        if frontmatter and 'title' in frontmatter:
            topic = frontmatter['title']
        else:
            topic = file_stem.replace('_', ' ').title()

        # Extract numbers
        numbers, categorized = extract_numbers_from_summary(summary_text, topic)

        if numbers:
            # Create output data structure
            numbers_data = {
                "topic": topic,
                "source_file": str(summary_file.name),
                "extraction_date": datetime.now().isoformat(),
                "total_numbers": len(numbers),
                "short_numbers": len(categorized['short']),
                "medium_numbers": len(categorized['medium']),
                "long_numbers": len(categorized['long']),
                "numbers": numbers,
                "categorized": categorized
            }

            # Save numbers to JSON file
            output_file = output_dir / f"{file_stem}_numbers.json"
            write_json_file(output_file, numbers_data)
            print(f"Saved {len(numbers)} unique numbers to {output_file}")
        else:
            print(f"No numbers (4+ digits) found in '{topic}'")

    print("Number extraction complete!")

if __name__ == "__main__":
    main()
