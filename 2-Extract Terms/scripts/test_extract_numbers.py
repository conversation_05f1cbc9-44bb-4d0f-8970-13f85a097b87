#!/usr/bin/env python3
"""
Test script for the number extraction functionality
"""

from extract_numbers import extract_numbers_from_text, categorize_numbers_by_length

def test_number_extraction():
    """Test the number extraction with various examples."""
    
    test_text = """
    The population grew from 1,234 people in 1850 to 45,678 by 1900.
    The cost was $2,403 for the project, while another cost 12345 dollars.
    In the year 1776, there were approximately 2,500,000 colonists.
    The distance was 123 miles (too short to extract).
    Large numbers like 1,234,567,890 were also recorded.
    Some numbers: 4000, 5,000, 67,890, and 1,000,000,000,000.
    """
    
    print("Test text:")
    print(test_text)
    print("\n" + "="*50)
    
    # Extract numbers
    numbers = extract_numbers_from_text(test_text)
    
    print(f"\nExtracted {len(numbers)} numbers:")
    for num_data in numbers:
        print(f"  '{num_data['original']}' -> '{num_data['cleaned']}' ({num_data['length']} digits)")
    
    # Test categorization
    print("\n" + "="*50)
    print("Categorization by length:")
    
    categorized = categorize_numbers_by_length(numbers)
    
    for category, nums in categorized.items():
        print(f"\n{category.upper()} ({len(nums)} numbers):")
        for num_data in nums:
            print(f"  {num_data['cleaned']} ({num_data['length']} digits)")

if __name__ == "__main__":
    test_number_extraction()
