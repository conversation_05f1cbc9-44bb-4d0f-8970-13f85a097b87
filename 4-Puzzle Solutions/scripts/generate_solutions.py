#!/usr/bin/env python3
"""
Number Search Puzzle Solution Generator

This script generates visual solutions for the number search puzzles created in Step 3.
"""

import sys
import json
import os
import re
import argparse
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir, read_json_file, write_json_file

try:
    from PIL import Image, ImageDraw, ImageFont
    import matplotlib.pyplot as plt
    from matplotlib.patches import Rectangle, FancyBboxPatch
    import numpy as np
except ImportError:
    print("Required libraries not found. Please install them with:")
    print("pip install pillow matplotlib numpy")
    sys.exit(1)

def load_puzzle(file_path):
    """Load a number search puzzle from a JSON file."""
    data = read_json_file(file_path)
    return data

def generate_solution_image(puzzle_data, output_file):
    """Generate a solution image for a number search puzzle."""
    # Extract puzzle data
    topic = puzzle_data.get("topic", "Number Search")
    grid = puzzle_data.get("grid", [])
    numbers = puzzle_data.get("numbers", [])
    number_locations = puzzle_data.get("number_locations", [])

    # Set up the figure and grid with a bit more space at the bottom
    fig, ax = plt.subplots(figsize=(10, 10.5))  # Slightly taller figure

    # Remove all titles - they will be added in the document generation step
    fig.suptitle('')
    ax.set_title('')

    # Add a bit more padding at the bottom
    plt.subplots_adjust(bottom=0.05)

    # Draw the grid
    grid_size = len(grid)
    for i in range(grid_size + 1):
        ax.axhline(i, color='black', linewidth=1)
        ax.axvline(i, color='black', linewidth=1)

    # Add digits to the grid
    for i in range(grid_size):
        for j in range(grid_size):
            ax.text(j + 0.5, grid_size - i - 0.5, grid[i][j],
                    ha='center', va='center', fontsize=24, fontweight='bold')

    # Highlight number locations with capsule shapes
    for number_info in number_locations:
        number = number_info.get("number", "")
        start = number_info.get("start", [0, 0])
        end = number_info.get("end", [0, 0])

        # Convert to matplotlib coordinates (y-axis is inverted)
        start_x, start_y = start[1], grid_size - start[0] - 1
        end_x, end_y = end[1], grid_size - end[0] - 1

        # Calculate the path of the word
        if start_x == end_x:  # Vertical
            x_points = [start_x + 0.5] * (abs(end_y - start_y) + 1)
            y_points = np.linspace(start_y + 0.5, end_y + 0.5, abs(end_y - start_y) + 1)
        elif start_y == end_y:  # Horizontal
            x_points = np.linspace(start_x + 0.5, end_x + 0.5, abs(end_x - start_x) + 1)
            y_points = [start_y + 0.5] * (abs(end_x - start_x) + 1)
        else:  # Diagonal
            x_points = np.linspace(start_x + 0.5, end_x + 0.5, abs(end_x - start_x) + 1)
            y_points = np.linspace(start_y + 0.5, end_y + 0.5, abs(end_y - start_y) + 1)

        # Draw the capsule shape for all orientations (horizontal, vertical, diagonal)
        # Calculate the angle of the word path
        if len(x_points) > 1:
            dx = x_points[-1] - x_points[0]
            dy = y_points[-1] - y_points[0]
            angle = np.degrees(np.arctan2(dy, dx))
        else:
            angle = 0

        # Use the approach from the provided SVG code with MUCH thicker lines
        # Draw a very thick line with rounded ends to create a capsule shape

        # Set the width of the capsule to 60% of a cell
        capsule_width = 0.6

        # Create a line between the start and end points of the word
        # For single letter words, we'll create a small line
        if len(x_points) == 1:
            # For single letter words, create a small line
            start_x = x_points[0] - 0.4  # Extend further
            start_y = y_points[0]
            end_x = x_points[0] + 0.4  # Extend further
            end_y = y_points[0]
        else:
            # For multi-letter words, extend beyond the start and end points
            # Calculate the direction vector
            dx = x_points[-1] - x_points[0]
            dy = y_points[-1] - y_points[0]

            # Normalize the direction vector
            length = np.sqrt(dx**2 + dy**2)
            if length > 0:
                dx = dx / length
                dy = dy / length
            else:
                dx, dy = 0, 0

            # Extend the line by 0.2 cells in each direction
            extension = 0.2
            start_x = x_points[0] - dx * extension
            start_y = y_points[0] - dy * extension
            end_x = x_points[-1] + dx * extension
            end_y = y_points[-1] + dy * extension

        # Calculate points per inch for scaling
        points_per_inch = 72.0
        # Calculate the figure's DPI
        dpi = fig.dpi if fig.dpi else 100
        # Calculate the cell size in points
        cell_size_points = 1.0 * points_per_inch / dpi
        # Calculate the line width in points (make it twice as wide)
        line_width_points = cell_size_points * 65  # Make it MUCH thicker

        # Create a thick line with rounded ends - using full opacity
        line = plt.Line2D(
            [start_x, end_x], [start_y, end_y],
            linewidth=line_width_points,  # Very thick line
            solid_capstyle='round',  # Rounded ends like in the SVG
            color='lightgray',
            alpha=1.0,  # Fully opaque to prevent transparency issues at intersections
            zorder=1
        )

        # Add a black border
        border = plt.Line2D(
            [start_x, end_x], [start_y, end_y],
            linewidth=line_width_points + 1,  # Slightly wider for border
            solid_capstyle='round',  # Rounded ends
            color='black',
            alpha=1.0,  # Fully opaque
            zorder=1  # Draw behind the main line
        )

        # Add the lines to the plot
        ax.add_line(border)
        ax.add_line(line)

    # Set axis limits and remove ticks
    ax.set_xlim(0, grid_size)
    ax.set_ylim(0, grid_size)
    ax.set_xticks([])
    ax.set_yticks([])

    # Save the figure with extra padding to ensure nothing is cut off
    plt.tight_layout(pad=1.2)  # Increase padding
    plt.savefig(output_file, dpi=300, bbox_inches='tight', pad_inches=0.2)  # Add extra padding
    plt.close()

    return output_file

def check_missing_numbers(puzzle_data):
    """Check if any numbers are missing from the puzzle."""
    numbers = puzzle_data.get("numbers", [])
    number_locations = puzzle_data.get("number_locations", [])

    # Get the numbers that have locations
    placed_numbers = [number_info.get("number", "") for number_info in number_locations]

    # Find missing numbers
    missing_numbers = [number for number in numbers if number not in placed_numbers]

    return missing_numbers

def main():
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Generate number search puzzle solutions")
    parser.add_argument("--topic", help="Topic name for the solution")
    parser.add_argument("--puzzle-file", help="Path to the puzzle file")
    parser.add_argument("--output-dir", help="Directory to save the output files")
    args = parser.parse_args()

    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = Path(args.output_dir) if args.output_dir else base_dir / 'output'
    puzzle_dir = Path(__file__).resolve().parents[2] / '3-Puzzle Generation' / 'output'

    # Ensure directories exist
    ensure_dir(input_dir)
    ensure_dir(output_dir)

    # Process a single puzzle file if specified
    if args.topic and args.puzzle_file:
        topic_name = args.topic
        puzzle_file = Path(args.puzzle_file)

        if not puzzle_file.exists():
            print(f"Error: Puzzle file not found: {puzzle_file}")
            return 1

        print(f"Generating solution for: {topic_name}")

        # Load puzzle and generate solution
        puzzle_data = load_puzzle(puzzle_file)
        output_file = output_dir / f"{topic_name}_solution.svg"
        generate_solution_image(puzzle_data, output_file)

        print(f"Solution saved to {output_file}")
        return 0

    # Otherwise, process all puzzle files
    puzzle_files = list(puzzle_dir.glob('*_number_search.json'))
    if not puzzle_files:
        print(f"No puzzle files found in {puzzle_dir}")
        return 1

    print(f"Found {len(puzzle_files)} puzzle files to process")

    # Process each puzzle file
    for puzzle_file in puzzle_files:
        # Get the topic name from the puzzle file name
        topic_name = puzzle_file.stem.replace('_number_search', '')

        # Remove timestamp from the topic name (e.g., "elvis_presley 8.30.34 AM" -> "elvis_presley")
        timestamp_match = re.search(r'\s+\d+\.\d+\.\d+\s+[AP]M', topic_name)
        if timestamp_match:
            topic_name = topic_name[:timestamp_match.start()]
        print(f"Generating solution for: {topic_name}")

        # Load puzzle and generate solution
        puzzle_data = load_puzzle(puzzle_file)
        output_file = output_dir / f"{topic_name}_solution.svg"
        generate_solution_image(puzzle_data, output_file)

        print(f"Solution saved to {output_file}")

        # Check for missing numbers
        missing_numbers = check_missing_numbers(puzzle_data)
        if missing_numbers:
            print(f"  Warning: {len(missing_numbers)} numbers not found in the puzzle:")
            for number in missing_numbers:
                print(f"    - {number}")

            # Save missing numbers to a file
            missing_file = output_dir / f"{topic_name}_missing_numbers.txt"
            with open(missing_file, 'w') as f:
                f.write(f"Missing numbers for {puzzle_data.get('topic', topic_name)}:\n")
                for number in missing_numbers:
                    f.write(f"{number}\n")
            print(f"  Missing numbers saved to {missing_file}")

        # Generate solution image
        output_file = output_dir / f"{topic_name}_solution.png"
        generate_solution_image(puzzle_data, output_file)
        print(f"  Solution saved to {output_file}")

    print("\nSolution generation complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())