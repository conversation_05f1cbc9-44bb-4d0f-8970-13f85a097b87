#!/usr/bin/env python3
"""
Number Search Puzzle Solution Generator

This script generates visual solutions for the number search puzzles created in Step 3.
"""

import sys
import json
import os
import re
import argparse
from pathlib import Path
from datetime import datetime

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir, read_json_file, write_json_file

try:
    from PIL import Image, ImageDraw, ImageFont
    import matplotlib.pyplot as plt
    from matplotlib.patches import Rectangle, FancyBboxPatch
    import numpy as np
except ImportError:
    print("Required libraries not found. Please install them with:")
    print("pip install pillow matplotlib numpy")
    sys.exit(1)

def load_puzzle(file_path):
    """Load a number search puzzle from a JSON file."""
    data = read_json_file(file_path)
    return data

def load_math_problems(puzzle_name):
    """Load the corresponding math problems for a puzzle."""
    # Try to find the math problems file
    math_file_paths = [
        Path(f"../1-Summary Generation/output/{puzzle_name.lower().replace(' ', '_')}.md"),
        Path(f"1-Summary Generation/output/{puzzle_name.lower().replace(' ', '_')}.md"),
        Path(f"/Users/<USER>/Number Search Book Generation/1-Summary Generation/output/{puzzle_name.lower().replace(' ', '_')}.md")
    ]

    for path in math_file_paths:
        if path.exists():
            # Read the file and extract YAML frontmatter
            with open(path, 'r') as f:
                content = f.read()

            # Extract YAML frontmatter
            import yaml
            if content.startswith('---'):
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    try:
                        frontmatter = yaml.safe_load(parts[1])
                        return frontmatter
                    except yaml.YAMLError:
                        pass

    return None

def format_math_solutions(frontmatter):
    """Format math solutions in 2-column layout without commas."""
    if not frontmatter:
        return "Math problems not found"

    problems = frontmatter.get('problems', [])
    answers = frontmatter.get('answers', [])
    operations = frontmatter.get('operations', [])

    if not problems or not answers or not operations:
        return "Math data incomplete"

    # Operation symbols
    op_symbols = {
        'addition': '+',
        'subtraction': '-',
        'multiplication': 'x',  # Using 'x' instead of '×' for compatibility
        'division': '÷'
    }

    # Format solutions in 2 columns (left: problems 1-10, right: problems 11-20)
    solution_text = ""

    for i in range(10):
        # Left column (problems 1-10)
        if i < len(problems):
            left_operands = problems[i]
            left_answer = answers[i]
            left_op = operations[i]
            left_symbol = op_symbols.get(left_op, '+')
            # Remove commas from numbers
            left_text = f"{left_operands[0]} {left_symbol} {left_operands[1]} = {left_answer}"
        else:
            left_text = ""

        # Right column (problems 11-20)
        right_idx = i + 10
        if right_idx < len(problems):
            right_operands = problems[right_idx]
            right_answer = answers[right_idx]
            right_op = operations[right_idx]
            right_symbol = op_symbols.get(right_op, '+')
            # Remove commas from numbers
            right_text = f"{right_operands[0]} {right_symbol} {right_operands[1]} = {right_answer}"
        else:
            right_text = ""

        # Add both solutions side by side
        solution_text += f"{left_text:<35} {right_text}\n"

    return solution_text.strip()

def generate_solution_document(puzzle_data, output_file):
    """Generate a solution document for a number search puzzle with math solutions."""
    # Extract puzzle data
    topic = puzzle_data.get("topic", "Number Search")
    grid = puzzle_data.get("grid", [])
    numbers = puzzle_data.get("numbers", [])
    number_locations = puzzle_data.get("number_locations", [])

    # Load corresponding math problems
    math_frontmatter = load_math_problems(topic)
    math_solutions = format_math_solutions(math_frontmatter)

    # Create a figure with subplots for the layout - much larger for readability
    # We'll create a 2x2 grid: Title (spanning), Math Solutions, Puzzle Grid, Empty
    fig = plt.figure(figsize=(20, 14))

    # Create a grid layout
    gs = fig.add_gridspec(3, 2, height_ratios=[0.8, 10, 0.5], hspace=0.2, wspace=0.3)

    # Title (spanning both columns)
    title_ax = fig.add_subplot(gs[0, :])
    title_ax.text(0.5, 0.5, f"{topic} Solutions", ha='center', va='center',
                  fontsize=28, fontweight='bold', transform=title_ax.transAxes)
    title_ax.axis('off')

    # Math Solutions (left column)
    math_ax = fig.add_subplot(gs[1, 0])
    math_ax.text(0.05, 0.98, "Math Solutions:", ha='left', va='top',
                 fontsize=18, fontweight='bold', transform=math_ax.transAxes)
    math_ax.text(0.05, 0.90, math_solutions, ha='left', va='top',
                 fontsize=14, fontfamily='monospace', transform=math_ax.transAxes)
    math_ax.axis('off')

    # Puzzle Grid (right column)
    puzzle_ax = fig.add_subplot(gs[1, 1])
    puzzle_ax.text(0.5, 0.98, "Number Search Solution:", ha='center', va='top',
                   fontsize=18, fontweight='bold', transform=puzzle_ax.transAxes)

    # Draw the grid in the puzzle subplot
    grid_size = len(grid)

    # Set up the puzzle grid area (leaving space for title)
    puzzle_ax.set_xlim(0, grid_size)
    puzzle_ax.set_ylim(0, grid_size)
    puzzle_ax.set_aspect('equal')

    # Draw grid lines
    for i in range(grid_size + 1):
        puzzle_ax.axhline(i, color='black', linewidth=1)
        puzzle_ax.axvline(i, color='black', linewidth=1)

    # Add digits to the grid
    for i in range(grid_size):
        for j in range(grid_size):
            puzzle_ax.text(j + 0.5, grid_size - i - 0.5, grid[i][j],
                          ha='center', va='center', fontsize=16, fontweight='bold')

    # Highlight number locations with capsule shapes
    for number_info in number_locations:
        number = number_info.get("number", "")
        start = number_info.get("start", [0, 0])
        end = number_info.get("end", [0, 0])

        # Convert to matplotlib coordinates (y-axis is inverted)
        start_x, start_y = start[1], grid_size - start[0] - 1
        end_x, end_y = end[1], grid_size - end[0] - 1

        # Calculate the path of the word
        if start_x == end_x:  # Vertical
            x_points = [start_x + 0.5] * (abs(end_y - start_y) + 1)
            y_points = np.linspace(start_y + 0.5, end_y + 0.5, abs(end_y - start_y) + 1)
        elif start_y == end_y:  # Horizontal
            x_points = np.linspace(start_x + 0.5, end_x + 0.5, abs(end_x - start_x) + 1)
            y_points = [start_y + 0.5] * (abs(end_x - start_x) + 1)
        else:  # Diagonal
            x_points = np.linspace(start_x + 0.5, end_x + 0.5, abs(end_x - start_x) + 1)
            y_points = np.linspace(start_y + 0.5, end_y + 0.5, abs(end_y - start_y) + 1)

        # Draw the capsule shape for all orientations (horizontal, vertical, diagonal)
        # Calculate the angle of the word path
        if len(x_points) > 1:
            dx = x_points[-1] - x_points[0]
            dy = y_points[-1] - y_points[0]
            angle = np.degrees(np.arctan2(dy, dx))
        else:
            angle = 0

        # Use the approach from the provided SVG code with MUCH thicker lines
        # Draw a very thick line with rounded ends to create a capsule shape

        # Set the width of the capsule to 60% of a cell
        capsule_width = 0.6

        # Create a line between the start and end points of the word
        # For single letter words, we'll create a small line
        if len(x_points) == 1:
            # For single letter words, create a small line
            start_x = x_points[0] - 0.4  # Extend further
            start_y = y_points[0]
            end_x = x_points[0] + 0.4  # Extend further
            end_y = y_points[0]
        else:
            # For multi-letter words, extend beyond the start and end points
            # Calculate the direction vector
            dx = x_points[-1] - x_points[0]
            dy = y_points[-1] - y_points[0]

            # Normalize the direction vector
            length = np.sqrt(dx**2 + dy**2)
            if length > 0:
                dx = dx / length
                dy = dy / length
            else:
                dx, dy = 0, 0

            # Extend the line by 0.2 cells in each direction
            extension = 0.2
            start_x = x_points[0] - dx * extension
            start_y = y_points[0] - dy * extension
            end_x = x_points[-1] + dx * extension
            end_y = y_points[-1] + dy * extension

        # Calculate points per inch for scaling
        points_per_inch = 72.0
        # Calculate the figure's DPI
        dpi = fig.dpi if fig.dpi else 100
        # Calculate the cell size in points
        cell_size_points = 1.0 * points_per_inch / dpi
        # Calculate the line width in points (make it twice as wide)
        line_width_points = cell_size_points * 65  # Make it MUCH thicker

        # Create a thick line with rounded ends - using full opacity
        line = plt.Line2D(
            [start_x, end_x], [start_y, end_y],
            linewidth=line_width_points,  # Very thick line
            solid_capstyle='round',  # Rounded ends like in the SVG
            color='lightgray',
            alpha=1.0,  # Fully opaque to prevent transparency issues at intersections
            zorder=1
        )

        # Add a black border
        border = plt.Line2D(
            [start_x, end_x], [start_y, end_y],
            linewidth=line_width_points + 1,  # Slightly wider for border
            solid_capstyle='round',  # Rounded ends
            color='black',
            alpha=1.0,  # Fully opaque
            zorder=1  # Draw behind the main line
        )

        # Add the lines to the plot
        puzzle_ax.add_line(border)
        puzzle_ax.add_line(line)

    # Remove ticks from puzzle grid
    puzzle_ax.set_xticks([])
    puzzle_ax.set_yticks([])

    # Save the figure
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', pad_inches=0.2)
    plt.close()

    return output_file

def check_missing_numbers(puzzle_data):
    """Check if any numbers are missing from the puzzle."""
    numbers = puzzle_data.get("numbers", [])
    number_locations = puzzle_data.get("number_locations", [])

    # Get the numbers that have locations
    placed_numbers = [number_info.get("number", "") for number_info in number_locations]

    # Find missing numbers
    missing_numbers = [number for number in numbers if number not in placed_numbers]

    return missing_numbers

def main():
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Generate number search puzzle solutions")
    parser.add_argument("--topic", help="Topic name for the solution")
    parser.add_argument("--puzzle-file", help="Path to the puzzle file")
    parser.add_argument("--output-dir", help="Directory to save the output files")
    args = parser.parse_args()

    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    input_dir = base_dir / 'input'
    output_dir = Path(args.output_dir) if args.output_dir else base_dir / 'output'
    puzzle_dir = Path(__file__).resolve().parents[2] / '3-Puzzle Generation' / 'output'

    # Ensure directories exist
    ensure_dir(input_dir)
    ensure_dir(output_dir)

    # Process a single puzzle file if specified
    if args.topic and args.puzzle_file:
        topic_name = args.topic
        puzzle_file = Path(args.puzzle_file)

        if not puzzle_file.exists():
            print(f"Error: Puzzle file not found: {puzzle_file}")
            return 1

        print(f"Generating solution for: {topic_name}")

        # Load puzzle and generate solution
        puzzle_data = load_puzzle(puzzle_file)
        output_file = output_dir / f"{topic_name}_solution.png"
        generate_solution_document(puzzle_data, output_file)

        print(f"Solution saved to {output_file}")
        return 0

    # Otherwise, process all puzzle files
    puzzle_files = list(puzzle_dir.glob('*_number_search.json'))
    if not puzzle_files:
        print(f"No puzzle files found in {puzzle_dir}")
        return 1

    print(f"Found {len(puzzle_files)} puzzle files to process")

    # Process each puzzle file
    for puzzle_file in puzzle_files:
        # Get the topic name from the puzzle file name
        topic_name = puzzle_file.stem.replace('_number_search', '')

        # Remove timestamp from the topic name (e.g., "elvis_presley 8.30.34 AM" -> "elvis_presley")
        timestamp_match = re.search(r'\s+\d+\.\d+\.\d+\s+[AP]M', topic_name)
        if timestamp_match:
            topic_name = topic_name[:timestamp_match.start()]
        print(f"Generating solution for: {topic_name}")

        # Load puzzle and generate solution
        puzzle_data = load_puzzle(puzzle_file)

        # Check for missing numbers
        missing_numbers = check_missing_numbers(puzzle_data)
        if missing_numbers:
            print(f"  Warning: {len(missing_numbers)} numbers not found in the puzzle:")
            for number in missing_numbers:
                print(f"    - {number}")

            # Save missing numbers to a file
            missing_file = output_dir / f"{topic_name}_missing_numbers.txt"
            with open(missing_file, 'w') as f:
                f.write(f"Missing numbers for {puzzle_data.get('topic', topic_name)}:\n")
                for number in missing_numbers:
                    f.write(f"{number}\n")
            print(f"  Missing numbers saved to {missing_file}")

        # Generate solution document
        output_file = output_dir / f"{topic_name}_solution.png"
        generate_solution_document(puzzle_data, output_file)
        print(f"  Solution saved to {output_file}")

    print("\nSolution generation complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())