#!/usr/bin/env python3
"""
Document Generation Script

This script generates a DOCX document with all the topics, puzzles, and solutions
according to the specified format.

The document includes:
- 7 blank pages at the beginning
- Topic pages with summaries and puzzles
- Puzzle grids with equal-sized cells (0.44 in × 0.44 in)
- Term lists in 3-column tables
- A blank page after all topics
- The "PUZZLE SOLUTIONS" page
- Solution images arranged in a 2x2 grid
- 2 blank pages at the end
"""

import sys
import re
from pathlib import Path

# Add the project root to the Python path to import from common
sys.path.append(str(Path(__file__).resolve().parents[2]))
from common.utils import ensure_dir, read_json_file, read_markdown_file

# Import docx library
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

def get_topic_files():
    """Get all topic files from the various directories."""
    topics = []

    # Get summaries - try different paths
    possible_paths = [
        Path("../1-Summary Generation/output"),  # Relative from script directory
        Path("1-Summary Generation/output"),     # Relative from project root
        Path("/Users/<USER>/Book Generation/1-Summary Generation/output")  # Absolute path
    ]

    summary_dir = None
    for path in possible_paths:
        if path.exists():
            summary_dir = path
            print(f"Found summary directory at {summary_dir}")
            break

    if not summary_dir:
        print("Summary directory not found. Tried:")
        for path in possible_paths:
            print(f"  {path}")
        print("Checking current directory structure...")
        print(f"Current directory: {Path.cwd()}")
        print(f"Parent directory: {Path.cwd().parent}")
        print("Listing directories:")
        for d in Path.cwd().parent.iterdir():
            if d.is_dir():
                print(f"  {d.name}")
        return []

    summary_files = list(summary_dir.glob("*.md"))
    print(f"Found {len(summary_files)} summary files in {summary_dir}")

    for summary_file in summary_files:
        # Properly handle capitalization for special cases like 'II' in 'World War II'
        # Remove timestamp from the filename (e.g., "elvis_presley 8.30.34 AM" -> "elvis_presley")
        topic_name = summary_file.stem

        # Check if there's a timestamp pattern (space followed by digits and AM/PM)
        timestamp_match = re.search(r'\s+\d+\.\d+\.\d+\s+[AP]M', topic_name)
        if timestamp_match:
            topic_name = topic_name[:timestamp_match.start()]

        # Replace underscores with spaces
        topic_name = topic_name.replace("_", " ")

        # Apply title case but preserve certain acronyms
        words = topic_name.split()
        for i, word in enumerate(words):
            if word.lower() == "ii":
                words[i] = "II"
            elif word.lower() == "iii":
                words[i] = "III"
            elif word.lower() == "iv":
                words[i] = "IV"
            elif word.lower() == "usa":
                words[i] = "USA"
            else:
                words[i] = word.capitalize()

        topic_name = " ".join(words)

        # Find corresponding numbers file - try different paths
        numbers_file = None
        numbers_paths = [
            Path(f"../2-Extract Terms/output/{summary_file.stem}_numbers.json"),
            Path(f"2-Extract Terms/output/{summary_file.stem}_numbers.json"),
            Path(f"/Users/<USER>/Book Generation/2-Extract Terms/output/{summary_file.stem}_numbers.json")
        ]
        for path in numbers_paths:
            if path.exists():
                numbers_file = path
                break
        if not numbers_file:
            print(f"Numbers file not found for {summary_file.stem}")
            continue

        # Find corresponding puzzle file - try different paths
        puzzle_file = None
        puzzle_paths = [
            Path(f"../3-Puzzle Generation/output/{summary_file.stem}_number_search.json"),
            Path(f"3-Puzzle Generation/output/{summary_file.stem}_number_search.json"),
            Path(f"/Users/<USER>/Book Generation/3-Puzzle Generation/output/{summary_file.stem}_number_search.json")
        ]
        for path in puzzle_paths:
            if path.exists():
                puzzle_file = path
                break
        if not puzzle_file:
            print(f"Puzzle file not found for {summary_file.stem}")
            continue

        # Find corresponding solution file - try different paths
        solution_file = None
        solution_paths = [
            Path(f"../4-Puzzle Solutions/output/{summary_file.stem}_solution.png"),
            Path(f"4-Puzzle Solutions/output/{summary_file.stem}_solution.png"),
            Path(f"/Users/<USER>/Book Generation/4-Puzzle Solutions/output/{summary_file.stem}_solution.png")
        ]
        for path in solution_paths:
            if path.exists():
                solution_file = path
                break
        if not solution_file:
            print(f"Solution file not found for {summary_file.stem}")
            continue

        # Extract order from frontmatter if available
        content = read_markdown_file(summary_file)
        frontmatter = extract_frontmatter(content)
        order = frontmatter.get("order", 9999)  # Default to a high number if order not found

        # All files exist at this point
        topics.append({
            "name": topic_name,
            "summary_file": summary_file,
            "numbers_file": numbers_file,
            "puzzle_file": puzzle_file,
            "solution_file": solution_file,
            "order": order
        })

    return topics

def extract_frontmatter(content):
    """Extract YAML frontmatter from markdown content."""
    yaml_pattern = re.compile(r'^---\s*\n(.*?)\n---\s*\n', re.DOTALL)
    match = yaml_pattern.match(content)

    if match:
        frontmatter_text = match.group(1)
        frontmatter = {}
        for line in frontmatter_text.strip().split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                try:
                    # Try to convert to int or float if applicable
                    if value.isdigit():
                        value = int(value)
                    elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                        value = float(value)
                except:
                    pass
                frontmatter[key] = value
        return frontmatter
    return {}

def extract_content_from_markdown(file_path):
    """Extract content from a markdown file."""
    content = read_markdown_file(file_path)

    # Extract YAML frontmatter if present
    yaml_pattern = re.compile(r'^---\s*\n(.*?)\n---\s*\n', re.DOTALL)
    match = yaml_pattern.match(content)

    if match:
        # Remove the frontmatter from the content
        content = content[match.end():]

    # Remove any markdown headings (# Title)
    content = re.sub(r'^#\s+.*?\n', '', content)

    return content.strip()

def add_page_number(run):
    """Add a page number field to the document."""
    fldChar1 = OxmlElement('w:fldChar')
    fldChar1.set(qn('w:fldCharType'), 'begin')

    instrText = OxmlElement('w:instrText')
    instrText.set(qn('xml:space'), 'preserve')
    instrText.text = "PAGE"

    fldChar2 = OxmlElement('w:fldChar')
    fldChar2.set(qn('w:fldCharType'), 'end')

    run._r.append(fldChar1)
    run._r.append(instrText)
    run._r.append(fldChar2)

def create_document(topics, output_file):
    """Create a DOCX document with all topics, puzzles, and solutions."""
    # Create a new document
    doc = Document()

    # Set up document properties
    section = doc.sections[0]
    section.top_margin = Inches(0.75)
    section.bottom_margin = Inches(0.75)
    section.left_margin = Inches(0.63)  # Inside margin
    section.right_margin = Inches(0.38)  # Outside margin

    # Set up default font
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Tahoma'
    font.size = Pt(16)
    font.color.rgb = RGBColor(0, 0, 0)  # Black

    # Add 7 blank pages at the beginning
    for _ in range(7):
        doc.add_paragraph()
        doc.add_page_break()

    # Process each topic
    for topic in topics:
        # Get topic data
        topic_name = topic["name"]
        summary_text = extract_content_from_markdown(topic["summary_file"])
        numbers_data = read_json_file(topic["numbers_file"])
        puzzle_data = read_json_file(topic["puzzle_file"])

        # First topic page: Title and summary
        title = doc.add_paragraph()
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title.add_run(topic_name)
        title_run.font.size = Pt(20)
        title_run.font.bold = True

        # Add instruction text
        instruction = doc.add_paragraph()
        instruction.alignment = WD_ALIGN_PARAGRAPH.CENTER
        instruction.add_run("Solve each problem below, then find the answers in the number search puzzle on the facing page.")

        # Add math problems formatted vertically
        add_vertical_math_problems(doc, topic["summary_file"])

        # Add page break
        doc.add_page_break()

        # Second topic page: Puzzle grid with title
        # Add the title at the top of the puzzle grid page
        puzzle_title = doc.add_paragraph()
        puzzle_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        puzzle_title_run = puzzle_title.add_run(topic_name)
        puzzle_title_run.font.size = Pt(20)
        puzzle_title_run.font.bold = True

        grid = puzzle_data.get("grid", [])

        # Create a table for the puzzle grid
        grid_table = doc.add_table(rows=len(grid), cols=len(grid[0]))
        grid_table.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Disable autofit behavior
        grid_table._tblPr.append(OxmlElement('w:tblLayout'))
        grid_table._tblPr.tblLayout.set(qn('w:type'), 'fixed')

        # Set table style with a 1pt outline around the entire grid
        grid_table.alignment = WD_TABLE_ALIGNMENT.CENTER

        # Remove all internal borders
        for row in grid_table.rows:
            for cell in row.cells:
                # Set all cell borders to none
                cell.border_top = None
                cell.border_bottom = None
                cell.border_left = None
                cell.border_right = None

        # Add a 1pt outline around the entire table
        tblBorders = OxmlElement('w:tblBorders')
        for border in ['top', 'left', 'bottom', 'right']:
            borderElement = OxmlElement(f'w:{border}')
            borderElement.set(qn('w:val'), 'single')
            borderElement.set(qn('w:sz'), '4')  # 4 = 1pt
            borderElement.set(qn('w:space'), '0')
            borderElement.set(qn('w:color'), 'auto')
            tblBorders.append(borderElement)

        # Add the borders to the table properties
        grid_table._tblPr.append(tblBorders)
        for row in grid_table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = RGBColor(0, 0, 0)  # Ensure text is black

        # Set cell dimensions to be square
        for row in grid_table.rows:
            # Set row height
            tr = row._tr
            trPr = tr.get_or_add_trPr()
            trHeight = OxmlElement('w:trHeight')
            trHeight.set(qn('w:val'), str(int(0.44 * 1440)))  # Convert inches to twips
            trHeight.set(qn('w:hRule'), 'exact')  # 'exact' means the height is exactly as specified
            trPr.append(trHeight)

            for cell in row.cells:
                # Set fixed cell width
                cell.width = Inches(0.44)

                # Center text in cell
                cell_para = cell.paragraphs[0]
                cell_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # Set vertical alignment
                tc = cell._tc
                tc.tcPr.append(OxmlElement('w:vAlign'))
                tc.tcPr.vAlign.set(qn('w:val'), "center")


        # Fill in the grid
        for i, row in enumerate(grid):
            for j, letter in enumerate(row):
                cell = grid_table.cell(i, j)
                para = cell.paragraphs[0]
                para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = para.add_run(letter)
                run.font.bold = True

        # We'll use a simple paragraph break with minimal spacing
        # This is just to separate the grid from the term table visually
        #spacing_para = doc.add_paragraph()
        #spacing_para.paragraph_format.space_before = Pt(0)
        #spacing_para.paragraph_format.space_after = Pt(0)

        # Note: We don't show the solution numbers on the puzzle page
        # Users need to solve the math problems to find the numbers to search for

        # Add page break after the puzzle grid
        doc.add_page_break()

    # Add a blank page after all topics
    #doc.add_paragraph()
    doc.add_page_break()

    # Add the "PUZZLE SOLUTIONS" page
    solutions_title = doc.add_paragraph()
    solutions_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    solutions_run = solutions_title.add_run("PUZZLE SOLUTIONS")
    solutions_run.font.size = Pt(24)
    solutions_run.font.bold = True

    # Add page break
    doc.add_page_break()

    # Add solution pages (2 solutions per page in a 2x1 grid for better readability)
    # We'll use the same order as the topics
    solution_images = []
    for topic in topics:
        solution_images.append({
            "name": topic["name"],
            "file": topic["solution_file"],
            "order": topic.get("order", 9999)
        })

    # Calculate how many solution pages we need
    solution_pages = (len(solution_images) + 1) // 2  # Ceiling division for 2 per page

    for page in range(solution_pages):
        # Get solutions for this page
        solutions_for_page = solution_images[page*2:page*2+2]

        # Create a table for the 2x2 grid (2 rows, 2 columns)
        # A1: Title + Math Solutions, A2: Puzzle Solution
        # B1: Title + Math Solutions, B2: Puzzle Solution
        if solutions_for_page:
            solution_table = doc.add_table(rows=2, cols=2)
            solution_table.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Set table style to have no borders
            solution_table.alignment = WD_TABLE_ALIGNMENT.CENTER

            # Set cell dimensions
            for row in solution_table.rows:
                for cell in row.cells:
                    cell.width = Inches(3.75)  # Half page width for 2 columns
                    cell.height = Inches(4.5)  # Good height for content

            # Process each solution for this page
            for i, solution in enumerate(solutions_for_page):
                if i < 2:  # Maximum 2 solutions per page
                    # Determine cell positions
                    # Solution 1: A1 (title+math) and A2 (puzzle)
                    # Solution 2: B1 (title+math) and B2 (puzzle)
                    title_math_cell = solution_table.cell(i, 0)  # Column 0: Title + Math Solutions
                    puzzle_cell = solution_table.cell(i, 1)      # Column 1: Puzzle Solution

                    # === TITLE + MATH SOLUTIONS CELL ===
                    # Add topic title with 18pt Tahoma bold font
                    title_para = title_math_cell.add_paragraph()
                    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    title_run = title_para.add_run(f"{solution['name']} - Math Solutions")
                    title_run.font.name = 'Tahoma'
                    title_run.font.size = Pt(16)
                    title_run.font.bold = True

                    # Add math solutions (we'll need to load these from the math file)
                    math_para = title_math_cell.add_paragraph()
                    math_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

                    # Load math solutions for this puzzle
                    math_solutions_text = load_math_solutions_for_puzzle(solution["name"])
                    math_run = math_para.add_run(math_solutions_text)
                    math_run.font.name = 'Courier New'
                    math_run.font.size = Pt(10)

                    # === PUZZLE SOLUTION CELL ===
                    # Add puzzle title
                    puzzle_title_para = puzzle_cell.add_paragraph()
                    puzzle_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    puzzle_title_run = puzzle_title_para.add_run(f"{solution['name']} - Puzzle Solution")
                    puzzle_title_run.font.name = 'Tahoma'
                    puzzle_title_run.font.size = Pt(16)
                    puzzle_title_run.font.bold = True

                    # Add puzzle solution image
                    puzzle_img_para = puzzle_cell.add_paragraph()
                    puzzle_img_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    puzzle_run = puzzle_img_para.add_run()
                    puzzle_run.add_picture(str(solution["file"]), width=Inches(3.5))

        # Add page break after each solution page (except the last one)
        if page < solution_pages - 1:
            doc.add_page_break()

    # Add two blank pages at the end
    doc.add_page_break()
    doc.add_paragraph()
    doc.add_page_break()

    # Add page numbers in the footer
    for section in doc.sections:
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT  # Align to right (outside corner)
        footer_run = footer_para.add_run()
        add_page_number(footer_run)

    # Save the document
    doc.save(output_file)

    return output_file

def load_math_problems_from_file(file_path):
    """Load math problems from a markdown file with YAML frontmatter."""
    with open(file_path, 'r') as f:
        content = f.read()

    # Extract YAML frontmatter
    import yaml
    if content.startswith('---'):
        parts = content.split('---', 2)
        if len(parts) >= 3:
            try:
                frontmatter = yaml.safe_load(parts[1])
                return frontmatter
            except yaml.YAMLError:
                pass

    return None

def load_math_solutions_for_puzzle(puzzle_name):
    """Load and format math solutions for a specific puzzle."""
    # Try to find the math problems file for this puzzle
    math_file_paths = [
        Path(f"../1-Summary Generation/output/{puzzle_name.lower().replace(' ', '_')}.md"),
        Path(f"1-Summary Generation/output/{puzzle_name.lower().replace(' ', '_')}.md"),
        Path(f"/Users/<USER>/Number Search Book Generation/1-Summary Generation/output/{puzzle_name.lower().replace(' ', '_')}.md")
    ]

    for path in math_file_paths:
        if path.exists():
            frontmatter = load_math_problems_from_file(path)
            if frontmatter:
                problems = frontmatter.get('problems', [])
                answers = frontmatter.get('answers', [])
                operations = frontmatter.get('operations', [])

                if problems and answers and operations:
                    return format_math_solutions_text(problems, answers, operations)

    return "Math solutions not found"

def format_math_solutions_text(problems, answers, operations):
    """Format math solutions as text for display in solution pages with numbering."""
    # Operation symbols
    op_symbols = {
        'addition': '+',
        'subtraction': '-',
        'multiplication': '×',
        'division': '÷'
    }

    # Format solutions in 2 columns (left: problems 1-10, right: problems 11-20)
    solution_text = ""

    for i in range(10):
        # Left column (problems 1-10)
        if i < len(problems):
            left_operands = problems[i]
            left_answer = answers[i]
            left_op = operations[i]
            left_symbol = op_symbols.get(left_op, '+')
            problem_num = i + 1  # 1-based numbering
            # Format with problem number and without commas for cleaner display
            left_text = f"{problem_num}) {left_operands[0]} {left_symbol} {left_operands[1]} = {left_answer}"
        else:
            left_text = ""

        # Right column (problems 11-20)
        right_idx = i + 10
        if right_idx < len(problems):
            right_operands = problems[right_idx]
            right_answer = answers[right_idx]
            right_op = operations[right_idx]
            right_symbol = op_symbols.get(right_op, '+')
            problem_num = right_idx + 1  # 1-based numbering
            # Format with problem number and without commas for cleaner display
            right_text = f"{problem_num}) {right_operands[0]} {right_symbol} {right_operands[1]} = {right_answer}"
        else:
            right_text = ""

        # Add both solutions side by side
        solution_text += f"{left_text:<45} {right_text}\n"  # Increased spacing for numbers

    return solution_text.strip()

def add_vertical_math_problems(doc, math_file_path):
    """Add math problems formatted vertically in a 2-column layout."""
    # Load math problems from file
    frontmatter = load_math_problems_from_file(math_file_path)
    if not frontmatter:
        doc.add_paragraph(f"Math problems not found for file: {math_file_path}")
        return

    problems = frontmatter.get('problems', [])
    operations = frontmatter.get('operations', [])

    if not problems or not operations:
        doc.add_paragraph(f"Math data incomplete - problems: {len(problems)}, operations: {len(operations)}")
        return

    # Math problems loaded successfully

    # Operation symbols
    op_symbols = {
        'addition': '+',
        'subtraction': '-',
        'multiplication': '×',
        'division': '÷'
    }

    # Create a table for the math problems (4 columns, 5 rows to fit all 20 problems)
    math_table = doc.add_table(rows=5, cols=4)
    math_table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Set exact column widths and row heights from your example on page 8
    # Use the exact dimensions you specified
    for col in math_table.columns:
        col.width = Inches(1.85)  # Exact width from your example

    for row in math_table.rows:
        row.height = Inches(1.2)  # Exact height from your example

    # Fill the table with vertically formatted math problems
    for i in range(5):  # 5 rows
        row = math_table.rows[i]

        # Fill each of the 4 columns
        for col in range(4):
            problem_idx = i * 4 + col  # Calculate problem index (0-19)

            if problem_idx < len(problems):
                cell = row.cells[col]
                operands = problems[problem_idx]  # This should be a list [operand1, operand2]
                operation = operations[problem_idx]
                symbol = op_symbols.get(operation, '+')

                # Format the problem vertically with numbering
                if len(operands) >= 2:
                    problem_number = problem_idx + 1  # 1-based numbering
                    add_vertical_problem_to_cell(cell, operands[0], operands[1], symbol, problem_number)

def add_vertical_problem_to_cell(cell, operand1, operand2, operator, problem_number):
    """Add a vertically formatted math problem to a table cell with numbering."""
    # Clear the cell
    cell.text = ""

    # Format numbers as strings
    num1_str = str(operand1)
    num2_str = str(operand2)

    # Use standard vertical format for all operations (+, -, ×, ÷)
    # Determine the width needed (longest number + operator)
    max_width = max(len(num1_str), len(num2_str) + 2)  # +2 for operator and space

    # Create the vertical format with problem number on same line as first operand
    # Calculate spacing to align the first operand properly while keeping number on left
    number_str = f"{problem_number})"
    # Create first line with number on left and operand on right
    # Use a fixed width for the total line and position elements
    total_width = max(max_width, 15)  # Ensure minimum width
    operand_width = total_width - 8  # Reserve 8 chars for number
    if operand_width < len(num1_str):
        operand_width = len(num1_str)
        total_width = operand_width + 8

    line1 = f"{number_str:<8}{num1_str:>{operand_width}}"  # Number left, operand right
    line2 = f"{operator} {num2_str:>{total_width-2}}"
    line3 = "─" * total_width
    line4 = " " * total_width  # Space for answer
    line5 = " " * total_width  # Additional line break
    line6 = " " * total_width  # Additional line break

    lines = [line1, line2, line3, line4, line5, line6]

    # Use the first paragraph for the combined number + first operand line
    para = cell.paragraphs[0]
    run = para.add_run(lines[0])  # Combined line with number and first operand
    run.font.name = 'Courier New'
    run.font.size = Pt(16)
    run.font.bold = True  # Bold for consistency with math problems
    para.alignment = WD_ALIGN_PARAGRAPH.LEFT  # Left-aligned to preserve number positioning

    # Apply the exact formatting from your manual example using paragraph format
    pf = para.paragraph_format
    pf.space_before = Pt(0)
    pf.space_after = Pt(0)  # After paragraph = 0 pt
    pf.line_spacing = 1.0   # Line = 1 (exact)

    # Add additional paragraphs for the remaining lines (math problem - bold and centered)
    for line in lines[1:]:
        para = cell.add_paragraph()
        run = para.add_run(line)
        run.font.name = 'Courier New'
        run.font.size = Pt(16)
        run.font.bold = True  # Bold the actual math problems
        para.alignment = WD_ALIGN_PARAGRAPH.CENTER  # Centered for math problems

        # Apply the same exact formatting to all paragraphs using paragraph format
        pf = para.paragraph_format
        pf.space_before = Pt(0)
        pf.space_after = Pt(0)  # After paragraph = 0 pt
        pf.line_spacing = 1.0   # Line = 1 (exact)

def main():
    # Parse command-line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Generate a DOCX document with all topics, puzzles, and solutions")
    parser.add_argument("--maintain-order", action="store_true", help="Maintain the order of topics from topics.txt")
    args = parser.parse_args()

    # Set up paths
    base_dir = Path(__file__).resolve().parents[1]
    output_dir = base_dir / 'output'

    # Ensure output directory exists
    ensure_dir(output_dir)

    # Get all topic files
    topics = get_topic_files()

    if not topics:
        print("No topics found. Please run the previous steps first.")
        return

    print(f"Found {len(topics)} topics to include in the document")

    # Sort topics by order if requested
    if args.maintain_order:
        topics.sort(key=lambda x: x.get("order", 9999))
        print("Topics sorted according to their original order in topics.txt")
    else:
        print("Using default alphabetical order for topics")

    # Create the output file path
    output_file = output_dir / "number_search_book.docx"

    # Create the document
    create_document(topics, output_file)

    print(f"Document generated successfully: {output_file}")
    print("\nNext steps:")
    print("1. Open the DOCX file in Pages")
    print("2. Enable 'Facing Pages' setting to get inside/outside margins")
    print("3. For each puzzle page, select the term table and:")
    print("   a. Go to Format > Arrange > Text Wrap > Automatic")
    print("   b. Position the table at x: 0.88 in, y: 7.99 in")
    print("4. Save as .pages format")
    print("5. Export as PDF if needed")

if __name__ == "__main__":
    main()
